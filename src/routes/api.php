<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/






/********************************************************************************
 *                                                                              *
 *                                                                              *
 *                               API Version: 1.1                               *
 *                                  01/05/2020                                  *
 *                                                                              *
 *                                                                              *
 ********************************************************************************/
Route::group([

    'prefix' => 'v1.1',

], function () {
    Session::put('API_VERSION', 'v1.1');
    require_once 'api_versions/v1.1.php';
});



/********************************************************************************
 *                                                                              *
 *                                                                              *
 *                               API Version: 1.0                               *
 *                                  01/04/2020                                  *
 *                                                                              *
 *                                                                              *
 ********************************************************************************/
Route::group([

    'prefix' => 'v1.0',

], function () {
    Session::put('API_VERSION', 'v1.0');
    require_once 'api_versions/v1.0.php';
});