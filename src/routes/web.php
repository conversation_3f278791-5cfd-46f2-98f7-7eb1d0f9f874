<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Http\Controllers\MTNSDPaymentGatewayController;
use App\Podcast;
use App\Slide;
use Illuminate\Support\Facades\DB;
use wapmorgan\Mp3Info\Mp3Info;

Route::get('/', function () {

    return view('welcome');
});


/**
 *      DSP Payment Gatway Api Controller
 */
Route::group(['prefix' => 'DSP', 'namespace' => 'Api'], function ($router) {
    Route::get('redirect/{phone}', 'PaymentGatwayController@redirectHE')->where('phone',  '^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$');
});

// MTN
Route::get('/mtn/redirect', [MTNSDPaymentGatewayController::class, 'redirect']);


Auth::routes();

Route::get('/download/file/{file}', 'FileController@download')->name('file.download');


Route::get('/dashboard', 'HomeController@index')->name('dashboard');


Route::group(['prefix' => 'dashboard', 'middleware' => 'auth'], function () {

    /**
     *
     *   Admin Controller
     *
     */
    Route::get('admins/', 'AdminController@index')->name('admin.index');
    Route::get('admins/create', 'AdminController@create')->name('admin.create');
    Route::post('admins/store', 'AdminController@store')->name('admin.store');
    Route::get('admins/edit/{id}', 'AdminController@edit')->name('admin.edit');
    Route::post('admins/update/{id}', 'AdminController@update')->name('admin.update');
    Route::delete('admins/destroy/{id}', 'AdminController@destroy')->name('admin.destroy');


    /**
     *
     *   User Controller
     *
     */
    Route::group(['prefix' => 'users'], function () {
        Route::get('/', 'UserController@index')->name('user.index');
        Route::delete('/unsubscribe/{user}', 'UserController@unsubscribe')->name('user.unsubscribe');
    });


    /**
     *
     *  Category Controller
     */

    Route::group(['prefix' => 'categories'], function () {
        Route::get('/', 'CategoryController@index')->name('category.index');
        Route::get('/json', 'CategoryController@indexJson')->name('category.index.json');
        Route::get('create', 'CategoryController@create')->name('category.create');
        Route::post('store', 'CategoryController@store')->name('category.store');
        Route::get('{category}', 'CategoryController@show')->name('category.show');
        Route::get('edit/{category}', 'CategoryController@edit')->name('category.edit');
        Route::post('update/{category}', 'CategoryController@update')->name('category.update');
        Route::delete('destroy/{category}', 'CategoryController@destroy')->name('category.destroy');
    });



    /**
     *
     *  Vendor Controller
     */

    Route::group(['prefix' => 'publishers'], function () {
        Route::get('/', 'VendorController@index')->name('vendor.index');
        Route::get('/json', 'VendorController@indexJson')->name('vendor.index.json');
        Route::get('create', 'VendorController@create')->name('vendor.create');
        Route::post('store', 'VendorController@store')->name('vendor.store');
        Route::get('{vendor}', 'VendorController@show')->name('vendor.show');
        Route::get('{vendor}/programs/json', 'VendorController@vendorProgramsJson')->name('vendor.show.programs.json');
        Route::get('edit/{vendor}', 'VendorController@edit')->name('vendor.edit');
        Route::post('update/{vendor}', 'VendorController@update')->name('vendor.update');
        Route::delete('destroy/{vendor}', 'VendorController@destroy')->name('vendor.destroy');
    });



    /**
     *
     *  Author Controller
     */

    Route::group(['prefix' => 'authors'], function () {
        Route::get('/', 'AuthorController@index')->name('author.index');
        Route::get('/json', 'AuthorController@indexJson')->name('author.index.json');
        Route::get('create', 'AuthorController@create')->name('author.create');
        Route::post('store', 'AuthorController@store')->name('author.store');
        Route::get('{author}', 'AuthorController@show')->name('author.show');
        Route::get('{author}/programs/json', 'AuthorController@authorProgramsJson')->name('author.show.programs.json');
        Route::get('edit/{author}', 'AuthorController@edit')->name('author.edit');
        Route::post('update/{author}', 'AuthorController@update')->name('author.update');
        Route::delete('destroy/{author}', 'AuthorController@destroy')->name('author.destroy');
    });



    /**
     *
     *  Book Controller
     */

    Route::group(['prefix' => 'books'], function () {
        Route::get('/', 'BookController@index')->name('book.index');
        Route::get('create', 'BookController@create')->name('book.create');
        Route::post('store', 'BookController@store')->name('book.store');
        Route::get('{book}', 'BookController@show')->name('book.show');
        Route::get('edit/{book}', 'BookController@edit')->name('book.edit');
        Route::post('update/{book}', 'BookController@update')->name('book.update');
        Route::get('toggle/publishing/{book}', 'BookController@togglePublishing')->name('book.toggle.publishing');
        Route::delete('destroy/{book}', 'BookController@destroy')->name('book.destroy');


        /**
         *
         *  Book Controller
         */

        Route::group(['prefix' => ''], function () {
            Route::get('{book}/episodes/create', 'EpisodeController@create')->name('episode.create');
            Route::post('{book}/episodes/store', 'EpisodeController@store')->name('episode.store');
            Route::get('{book}/episodes/show/{episode}', 'EpisodeController@show')->name('episode.show');
            Route::get('{book}/episodes/edit/{episode}', 'EpisodeController@edit')->name('episode.edit');
            Route::post('{book}/episodes/update/{episode}', 'EpisodeController@update')->name('episode.update');
            Route::delete('{book}/episodes/destroy/{episode}', 'EpisodeController@destroy')->name('episode.destroy');
        });

    });




    /**
     *
     *  Program Controller
     */

    Route::group(['prefix' => 'programs'], function () {
        Route::get('/', 'ProgramController@index')->name('program.index');
        Route::get('/json', 'ProgramController@indexJson')->name('program.index.json');
        Route::get('create', 'ProgramController@create')->name('program.create');
        Route::post('store', 'ProgramController@store')->name('program.store');
        Route::get('{program}', 'ProgramController@show')->name('program.show');
        Route::get('edit/{program}', 'ProgramController@edit')->name('program.edit');
        Route::post('update/{program}', 'ProgramController@update')->name('program.update');
        Route::delete('destroy/{program}', 'ProgramController@destroy')->name('program.destroy');

        /**
         *  Program Playlists Controller
         */

        Route::get('{program}/playlists/create', 'PlaylistController@create')->name('playlist.create');
        Route::post('{program}/playlists/store', 'PlaylistController@store')->name('playlist.store');
        Route::get('{program}/playlists/{playlist}', 'PlaylistController@show')->name('playlist.show');
        Route::get('{program}/playlists/edit/{playlist}', 'PlaylistController@edit')->name('playlist.edit');
        Route::post('{program}/playlists/update/{playlist}', 'PlaylistController@update')->name('playlist.update');
        Route::delete('{program}/playlists/destroy/{playlist}', 'PlaylistController@destroy')->name('playlist.destroy');
    });


    /**
     *
     *  Program Playlists Controller
     */

    Route::group(['prefix' => '/'], function () {
        Route::get('programs/{program}/playlists/create', 'PlaylistController@create')->name('playlist.create');
        Route::post('programs/{program}/playlists/store', 'PlaylistController@store')->name('playlist.store');
        Route::get('programs/{program}/playlists/{playlist}/add', 'PlaylistController@add')->name('playlist.add');
        Route::get('programs/playlists/podcasts/json', 'PlaylistController@searchPodcastsJson')->name('playlist.search.podcasts');
        Route::post('/playlists/{playlist}/podcasts/attach/json', 'PlaylistController@attach')->name('playlist.attach');
        Route::delete('/playlists/{playlist}/podcasts/{podcast}/detach/json', 'PlaylistController@detach')->name('playlist.detach');
        Route::get('programs/{program}/playlists/{playlist}', 'PlaylistController@show')->name('playlist.show');
        Route::get('programs/{program}/playlists/edit/{playlist}', 'PlaylistController@edit')->name('playlist.edit');
        Route::post('programs/{program}/playlists/update/{playlist}', 'PlaylistController@update')->name('playlist.update');
        Route::delete('programs/{program}/playlists/destroy/{playlist}', 'PlaylistController@destroy')->name('playlist.destroy');
    });


    /**
     *
     *  Slide Controller
     */

    Route::group(['prefix' => 'slides'], function () {
        Route::get('/', 'SlideController@index')->name('slide.index');
        Route::get('create', 'SlideController@create')->name('slide.create');
        Route::post('store', 'SlideController@store')->name('slide.store');
        Route::get('{slide}', 'SlideController@show')->name('slide.show');
        Route::get('edit/{slide}', 'SlideController@edit')->name('slide.edit');
        Route::post('update/{slide}', 'SlideController@update')->name('slide.update');
        Route::delete('destroy/{slide}', 'SlideController@destroy')->name('slide.destroy');
        Route::put('toggle-visibility/{slide}/json', 'SlideController@toggleVisibilityJson');
        Route::put('swap/{slide1}/{slide2}/json', 'SlideController@swapSequenceJson');
    });


    /**
     *  Slie Api Routes for Web AdminPanel
     */
    Route::group(['prefix' => 'slides', 'namespace' => 'Api'], function () {
        Route::put('toggle-visibility/{slide}', 'SlideApiController@toggleVisibility');
        Route::put('swap/{slide1}/{slide2}', 'SlideApiController@swapSequence');
    });


    /**
     *
     *  Podcast Controller
     */

    Route::group(['prefix' => 'podcasts'], function () {
        Route::get('/', 'PodcastController@index')->name('podcast.index');
        Route::get('create', 'PodcastController@create')->name('podcast.create');
        Route::post('store', 'PodcastController@store')->name('podcast.store');
        Route::get('{podcast}', 'PodcastController@show')->name('podcast.show');
        Route::get('edit/{podcast}', 'PodcastController@edit')->name('podcast.edit');
        Route::post('update/{podcast}', 'PodcastController@update')->name('podcast.update');
        Route::delete('destroy/{podcast}', 'PodcastController@destroy')->name('podcast.destroy');
    });


    /**
     *
     *  LiveStream Controller
     */

    Route::group(['prefix' => 'streams'], function () {
        Route::get('/', 'LiveStreamController@index')->name('stream.index');
        Route::get('create', 'LiveStreamController@create')->name('stream.create');
        Route::post('store', 'LiveStreamController@store')->name('stream.store');
        Route::get('{liveStream}', 'LiveStreamController@show')->name('stream.show');
        Route::get('edit/{liveStream}', 'LiveStreamController@edit')->name('stream.edit');
        Route::post('update/{liveStream}', 'LiveStreamController@update')->name('stream.update');
        Route::delete('destroy/{liveStream}', 'LiveStreamController@destroy')->name('stream.destroy');
    });


    /**
     *
     *  DynamicPage Controller
     */

    Route::group(['prefix' => 'pages'], function () {
        Route::get('/', 'DynamicPageController@index')->name('dynamicPage.index');
        Route::get('create', 'DynamicPageController@create')->name('dynamicPage.create');
        Route::post('store', 'DynamicPageController@store')->name('dynamicPage.store');
        Route::get('{dynamicPage}', 'DynamicPageController@show')->name('dynamicPage.show');
        Route::get('edit/{dynamicPage}', 'DynamicPageController@edit')->name('dynamicPage.edit');
        Route::get('toggle-visibility/{dynamicPage}', 'DynamicPageController@toggleVisibility')->name('dynamicPage.toggle.visibility');
        Route::post('update/{dynamicPage}', 'DynamicPageController@update')->name('dynamicPage.update');
        Route::delete('destroy/{dynamicPage}', 'DynamicPageController@destroy')->name('dynamicPage.destroy');
    });


    /**
     *
     *  Report Controller
     */

    Route::group(['prefix' => 'reports'], function () {
        Route::get('/podcasts', 'ReportController@trend')->name('report.podcasts');
        Route::get('/subscriptions-summary', 'ReportController@subscriptions_summary')->name('report.subscriptions_summary');
    });

    /**
     *
     *  Charts Controller
     */

    Route::group(['prefix' => 'charts'], function () {
        Route::get('/podcast/{podcast}', 'ChartController@podcast')->name('chart.podcast');
        Route::get('/podcasts', 'ChartController@podcasts')->name('chart.podcasts');
    });
});
