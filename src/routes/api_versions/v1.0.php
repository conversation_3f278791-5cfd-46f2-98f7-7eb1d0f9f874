<?php

use App\Http\Controllers\MTNSDPaymentGatewayController;

/********************************************************************************
 *                                                                              *
 *                                                                              *
 *                               API Version: 1.0                               *
 *                                  01/04/2020                                  *
 *                                                                              *
 *                                                                              *
 ********************************************************************************/


/**
 *      Auth Api Controller
 */
Route::group(['prefix' => 'auth', 'namespace' => 'Auth'], function ($router) {
    Route::post('logout', 'JWTController@logout');
    Route::post('refresh', 'JW<PERSON><PERSON>roller@refresh');
    Route::get('me', '<PERSON><PERSON><PERSON>ontroller@me');
});

/**
 *      DSP Payment Gatway Api Controller
 */
Route::group(['prefix' => 'DSP', 'namespace' => 'Api'], function ($router) {

    Route::post('login', 'PaymentGatwayController@login');
    Route::post('check-subscription', 'PaymentGatwayController@checkSubscription')->middleware(['auth:api']);
    Route::post('unsubscribe', 'PaymentGatwayController@unsubscribe')->middleware(['auth:api']);

    Route::match(['get', 'post'], 'push-notification', 'PaymentGatwayController@pushNotificationSubsStatus');

});

Route::group(['prefix' => 'mtn'], function () {
    Route::match(['get', 'post'], 'callback', [MTNSDPaymentGatewayController::class, 'callback']);
});

/**
 *      Required Auth Group
 */
Route::group([

    'middleware' => ['user.log'],
    'namespace' => 'Api'

], function () {


    /**
     *      Book Api Controller
     */
    Route::group(['prefix' => 'books'], function () {

        Route::get('/all', 'BookApiController@all');
        Route::get('/{book}/show', 'BookApiController@show');
        Route::get('/most-read', 'BookApiController@mostRead');
        Route::get('/newest', 'BookApiController@newest');
        Route::get('/{book}/likes', 'BookApiController@likesCount');
        Route::get('/{book}/user-stats', 'BookApiController@userStats')->middleware(['auth:api']);
        Route::get('/recommended', 'BookApiController@recommended');
        Route::post('/{book}/toggle-like', 'BookApiController@toggleLike')->middleware(['auth:api']);
        // Route::post('/{podcast}/recently-played/log', 'BookApiController@logToRecentlyPlayed');
        // Route::get('/library/history', 'BookApiController@history')->middleware(['auth:api']);
        // Route::get('/library/favorite', 'BookApiController@favorite')->middleware(['auth:api']);
    });

    /**
     *      Slide Api Controller
     */
    Route::group(['prefix' => 'slides'], function () {
        Route::get('/', 'SlideApiController@home');
    });


    // /**
    //  *      Program Api controller
    //  */
    // Route::group(['prefix' => 'programs'], function () {
    //     Route::get('/', 'ProgramApiController@index');
    //     Route::get('/trend', 'ProgramApiController@trend');
    //     Route::get('/{program}/podcasts', 'ProgramApiController@podcasts');
    // });

    /**
     *      Category Api Controller
     */
    Route::group(['prefix' => 'categories'], function () {

        Route::get('/', 'CategoryApiController@index');
        Route::get('/{category}/show', 'CategoryApiController@show');
        Route::get('/{category}/books', 'CategoryApiController@books');
    });


    /**
     *      DynamicPage Api Controller
     */
    Route::group(['prefix' => 'dynamic-pages'], function () {
        Route::get('/{uuid}/show', 'DynamicPageApiController@show');
    });

    /**
     *      Settings Api Controller
     */
    Route::group(['prefix' => 'settings'], function () {    
        Route::get('/my-preferences', 'SettingsApiController@myPreferences')->middleware(['auth:api']);
        Route::delete('/preference/{preference}', 'SettingsApiController@deletePreference')->middleware(['auth:api']);
    });

    /**
     *      Search Api Controller
     */
    Route::group(['prefix' => 'search'], function () {
        Route::get('/suggest', 'SearchApiController@suggest');
        Route::get('/my-logs', 'SearchApiController@searchLogs')->middleware(['auth:api']);
    });
});
