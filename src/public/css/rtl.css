html,
body {
    direction: rtl;
    text-align: right;
}

.fixed-sidebar .app-main .app-main__outer {
    padding-left: unset;
    padding-right: 200px;
}

.app-page-title .page-title-icon {
    margin: 0 0 0 30px;
}

.app-page-title .page-title-actions {

    margin-left: unset;
    margin-right: auto;

}

.app-header .app-header__content .app-header-right {
    margin-left: unset;
    margin-right: auto;
}

.vertical-nav-menu i.metismenu-state-icon,
.vertical-nav-menu i.metismenu-icon {
    left: unset;
    right: 5px;
}

.vertical-nav-menu li a {
    padding: 0 45px 0 1.5rem;
}

.vertical-nav-menu ul {
    padding: 0.5em 2rem 0 0;
}

.vertical-nav-menu ul:before {
    right: 20px;
    left: unset;
}

.vertical-nav-menu i.metismenu-state-icon {
    left: 0;
    right: auto;
}

.app-sidebar .scrollbar-sidebar {
    overflow: auto;
}

.search-wrapper .input-holder .search-icon {
    float: left;
}

.nav-link .nav-link-icon {
    margin-left: 0;
}


@media (max-width: 991.98px) {
    .app-main .app-main__outer {
        padding-right: 0 !important;
    }

    .app-sidebar {
        -webkit-transform: translateX(200px);
        transform: translateX(200px);
    }

    .sidebar-mobile-open .app-sidebar .app-sidebar__inner ul li a {
        text-indent: initial;
        padding: 0 45px 0 1.5rem;
    }

    .sidebar-mobile-open .app-sidebar .app-sidebar__inner ul ul li a {
        padding-left: 1em;
    }

    .sidebar-mobile-open .app-sidebar .app-sidebar__inner ul.mm-show {
        padding: 0.5em 2rem 0 0;
    }

    .app-main .app-main__inner {
        padding: 30px 30px 0;
    }
}

.search-wrapper .input-holder .search-input {
    padding: 0 30px 0 50px;
}
