<?php

use App\Program;
use App\Slide;
use Illuminate\Database\Seeder;

class SlidesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Slide::create([
        //     'slidable_id' => 1,
        //     'slidable_type' => Program::class,
        //     'sequence' => 1,
        // ]);
        // Slide::create([
        //     'slidable_id' => 2,
        //     'slidable_type' => Program::class,
        //     'sequence' => 2,
        //     'is_visible' => false,
        // ]);
        // Slide::create([
        //     'slidable_id' => 3,
        //     'slidable_type' => Program::class,
        //     'sequence' => 3,
        // ]);
        // Slide::create([
        //     'slidable_id' => 4,
        //     'slidable_type' => Program::class,
        //     'sequence' => 4,
        //     'is_visible' => false,
        // ]);
        // Slide::create([
        //     'slidable_id' => 5,
        //     'slidable_type' => Program::class,
        //     'sequence' => 5,
        // ]);
        // Slide::create([
        //     'slidable_id' => 6,
        //     'slidable_type' => Program::class,
        //     'sequence' => 6,
        // ]);
        // Slide::create([
        //     'slidable_id' => 7,
        //     'slidable_type' => Program::class,
        //     'sequence' => 7,
        // ]);
    }
}