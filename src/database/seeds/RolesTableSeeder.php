<?php

use App\Role;
use Illuminate\Database\Seeder;

class RolesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Prepare all roles models

        $roles = collect([

            // - Manage Admins
            [
                'code' => 'manage_admins',
                'name' => 'إدارة المشرفين',
            ],

            // - Manage Podcasts
            [
                'code' => 'manage_books',
                'name' => 'إدارة الكتب',
            ],

            // - Manage Program
            [
                'code' => 'manage_programs',
                'name' => 'إدارة برامج',
            ],

            // - Manage Categories
            [
                'code' => 'manage_categories',
                'name' => 'إدارة التصنيفات',
            ],

            // - Manage Slides
            [
                'code' => 'manage_slides',
                'name' => 'إدارة عرض الشرائح',
            ],

            // - Manage Reports
            [
                'code' => 'manage_reports',
                'name' => 'إدارة  التقارير',
            ],

            // - <PERSON><PERSON> Vendor
            [
                'code' => 'manage_vendors',
                'name' => 'إدارة  دور النشر',
            ],

            // - Manage Author
            [
                'code' => 'manage_authors',
                'name' => 'إدارة  المؤلفين',
            ],

            // - Manage Users
            [
                'code' => 'manage_users',
                'name' => 'إدارة  المشتركين',
            ],
        ]);

        // Save them all
        $this->createManyRoles($roles);
    }

    private function createManyRoles($roles)
    {
        foreach ($roles as $role) {
            Role::create($role);
        }
    }
}
