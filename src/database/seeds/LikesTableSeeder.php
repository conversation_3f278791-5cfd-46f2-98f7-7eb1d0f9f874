<?php

use App\Like;
use App\Podcast;
use Illuminate\Database\Seeder;

class LikesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        Like::create([
            'user_id' => 1,
            'likable_id' => 2,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 1,
            'likable_id' => 9,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 2,
            'likable_id' => 9,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 3,
            'likable_id' => 9,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 1,
            'likable_id' => 3,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 2,
            'likable_id' => 3,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 1,
            'likable_id' => 4,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 2,
            'likable_id' => 5,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 3,
            'likable_id' => 6,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 1,
            'likable_id' => 6,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 1,
            'likable_id' => 7,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 3,
            'likable_id' => 7,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 3,
            'likable_id' => 8,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 3,
            'likable_id' => 10,
            'likable_type' => Podcast::class,
        ]);

        Like::create([
            'user_id' => 2,
            'likable_id' => 10,
            'likable_type' => Podcast::class,
        ]);
    }
}