<?php

use App\Author;
use Illuminate\Database\Seeder;

class AuthorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Author::create([
            'name' => '<PERSON>',
        ]);

        Author::create([
            'name' => '<PERSON><PERSON>',
        ]);

        Author::create([
            'name' => '<PERSON>',
        ]);

        Author::create([
            'name' => '<PERSON>',
        ]);

        Author::create([
            'name' => '<PERSON>',
        ]);
    }
}
