<?php

use App\RecentlyPlayed;
use Illuminate\Database\Seeder;

class RecentlyPlayedsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        RecentlyPlayed::create([
            'user_id' => 1,
            'podcast_id' => 1,
            'progress' => 10000,
            'seconds' => 10,
        ]);
        RecentlyPlayed::create([
            'user_id' => 1,
            'podcast_id' => 1,
            'progress' => 20000,
            'seconds' => 10,
        ]);
        RecentlyPlayed::create([
            'user_id' => 1,
            'podcast_id' => 1,
            'progress' => 30000,
            'seconds' => 10,
        ]);
        RecentlyPlayed::create([
            'user_id' => 1,
            'podcast_id' => 1,
            'progress' => 40000,
            'seconds' => 10,
        ]);
        RecentlyPlayed::create([
            'user_id' => 1,
            'podcast_id' => 1,
            'progress' => 50000,
            'seconds' => 10,
        ]);
        RecentlyPlayed::create([
            'user_id' => 1,
            'podcast_id' => 1,
            'progress' => 60000,
            'seconds' => 10,
        ]);


        RecentlyPlayed::create([
            'user_id' => 1,
            'podcast_id' => 2,
            'progress' => 10000,
            'seconds' => 10,
        ]);

        RecentlyPlayed::create([
            'user_id' => 1,
            'podcast_id' => 3,
            'progress' => 10000,
            'seconds' => 10,
        ]);

        RecentlyPlayed::create([
            'user_id' => 2,
            'podcast_id' => 4,
            'progress' => 10000,
            'seconds' => 10,
        ]);

        RecentlyPlayed::create([
            'user_id' => 2,
            'podcast_id' => 5,
            'progress' => 10000,
            'seconds' => 10,
        ]);


        RecentlyPlayed::create([
            'user_id' => 1,
            'podcast_id' => 6,
            'progress' => 10000,
            'seconds' => 10,
        ]);


        RecentlyPlayed::create([
            'user_id' => 3,
            'podcast_id' => 7,
            'progress' => 10000,
            'seconds' => 10,
        ]);


        RecentlyPlayed::create([
            'user_id' => 3,
            'podcast_id' => 8,
            'progress' => 10000,
            'seconds' => 10,
        ]);

        RecentlyPlayed::create([
            'user_id' => 1,
            'podcast_id' => 9,
            'progress' => 10000,
            'seconds' => 10,
        ]);


        RecentlyPlayed::create([
            'user_id' => 1,
            'podcast_id' => 9,
            'progress' => 20000,
            'seconds' => 10,
        ]);


        RecentlyPlayed::create([
            'user_id' => 1,
            'podcast_id' => 9,
            'progress' => 30000,
            'seconds' => 10,
        ]);


        RecentlyPlayed::create([
            'user_id' => 2,
            'podcast_id' => 10,
            'progress' => 10000,
            'seconds' => 10,
        ]);
    }
}