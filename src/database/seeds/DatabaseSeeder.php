<?php

use App\Http\Resources\PodcastResource;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            RolesTableSeeder::class,
            UsersTableSeeder::class,
            AuthorSeeder::class,
            CategoriesTableSeeder::class,
            VendorSeeder::class,
            // ProgramsTableSeeder::class,
            // SlidesTableSeeder::class,
            // PlaylistsTableSeeder::class,
            // PodcastsTableSeeder::class,
            // LikesTableSeeder::class,
            // FollowsTableSeeder::class,
            // RecentlyPlayedsTableSeeder::class,
        ]);
    }
}
