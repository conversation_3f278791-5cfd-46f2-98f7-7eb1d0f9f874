<?php

use App\Role;
use App\User;
use Illuminate\Database\Seeder;

class UsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $user1 = User::create([
            'name' => 'Alkkhatib Hamad',
            'email' => '<EMAIL>',
            'phone' => '249113042009',
            'password' => bcrypt('12345678'),
            'type' => 'Subscriber',
        ]);

        $user2 = User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '249123456789',
            'password' => bcrypt('aziz@123'),
            'type' => 'Admin',
        ]);

        $roleIDs =Role::all()->pluck('id');

        $user1->roles()->sync($roleIDs);
        $user2->roles()->sync($roleIDs);
    }
}
