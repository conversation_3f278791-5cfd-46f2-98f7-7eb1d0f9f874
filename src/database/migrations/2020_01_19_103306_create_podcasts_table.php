<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePodcastsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('podcasts', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('program_id')->index();
            $table->string('name_ar');
            $table->string('name_en');
            $table->text('thumbnail');
            $table->integer('downloads')->default(0);
            $table->longText('short_description_ar')->nullable();
            $table->longText('short_description_en')->nullable();
            $table->longText('full_description_ar')->nullable();
            $table->longText('full_description_en')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('podcasts');
    }
}