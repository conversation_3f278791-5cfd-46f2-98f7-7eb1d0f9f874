<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOperatorToSubscriptionsAndBillingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->string('operator')->default('zain');
        });
        Schema::table('billings', function (Blueprint $table) {
            $table->string('operator')->default('zain');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn('operator');
        });
        Schema::table('billings', function (Blueprint $table) {
            $table->dropColumn('operator');
        });
    }
}
