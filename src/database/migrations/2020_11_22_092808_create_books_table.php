<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBooksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('books', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('category_id')->index();
            $table->unsignedBigInteger('vendor_id')->index();
            $table->string('title_ar');
            $table->string('title_en');
            $table->text('cover');
            $table->integer('downloads')->default(0);
            $table->longText('description_ar')->nullable();
            $table->longText('description_en')->nullable();
            $table->year('publishing_year');
            $table->integer('isbn');
            $table->integer('pages');
            $table->boolean('is_premium')->default(False);
            $table->string('country', 10);
            $table->string('language', 10);
            $table->timestamp('approved_at')->nullable();
            $table->integer('views')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('books');
    }
}
