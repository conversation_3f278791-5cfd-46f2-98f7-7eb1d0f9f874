<?php

use App\RecentlyPlayed;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePlatformColumnAndChangeUserIdInRecentlyPlayedsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('recently_playeds', function (Blueprint $table) {
            $table->string('platform', 50)->default(RecentlyPlayed::PLATFORM_ANDROID_APP);
            $table->bigInteger('user_id')->nullable()->change();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('recently_playeds', function (Blueprint $table) {
            //
        });
    }
}
