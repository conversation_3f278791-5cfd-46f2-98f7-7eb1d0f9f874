<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterBillingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('billings', function (Blueprint $table) {
            $table->renameColumn('billing_id', 'transaction_id');
            $table->renameColumn('price', 'amount');

            $table->timestamp('expiry_date')->nullable();

            $table->dropForeign('billings_package_id_foreign');
            $table->dropColumn('package_id');
            $table->dropColumn('subscription_duration');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('billings', function (Blueprint $table) {
            //
        });
    }
}
