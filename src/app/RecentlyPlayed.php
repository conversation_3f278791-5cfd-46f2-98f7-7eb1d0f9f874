<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class RecentlyPlayed extends Model
{
    protected $guarded = ['id'];

    public const PLATFORM_ANDROID_APP = 'android_app';
    public const PLATFORM_WEB_BROWSER = 'web_browser';

    /**
     *---------------------------------------
     *      Relations
     *---------------------------------------
     */

    /**
     *     Podcast relation
     */
    public function podcast()
    {
        return $this->belongsTo(Podcast::class);
    }
}
