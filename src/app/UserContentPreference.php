<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class UserContentPreference extends Model
{
    const CREATION_TYPE_MANUAL = 'manual';
    const CREATION_TYPE_RELATED = 'related';
    const CREATION_TYPE_SUGGESTED = 'suggested'; // suggested by system or advirting

    protected $guarded = ['id'];

    
    /**
     *---------------------------------------
     *      Relations
     *---------------------------------------
     */

    /**
     *     Perferable Content Relation
     */
    public function preferableContent()
    {
        return $this->morphTo();
    }
}
