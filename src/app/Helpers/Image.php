<?php

namespace App\Helpers;

class Image
{
    /*
    *   Array of allowed images extension can be uploaded.
    */
    protected static $allowedImageExtension = ['jpg', 'jpeg', 'png'];

    /**
     *  $allowedImageExtension in String format separated with $glue
     */
    public static function uploadableImageExtension($glue = ',')
    {
        return implode($glue, self::$allowedImageExtension);
    }
}