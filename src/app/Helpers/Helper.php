<?php

namespace App\Helpers;

use App\File;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;

class Helper
{

    public static function back($route, $params = [])
    {
        $lastPreviousKnown = URL::previous();

        return route($route, $params);

        // return $lastPreviousKnown == URL::current()
        //     ? route($route, $params)
        //     : $lastPreviousKnown;
    }

    public static function getFilePath($path)
    {
        return Storage::url($path);
    }

    public static function getSecureFileURL(File $file)
    {
        if(is_null($file)) return null;
        return '/download/file/'. $file->uuid;
    }

    public static function tableIndex($collection, $loop)
    {
        return (($collection->currentPage() - 1) * $collection->perPage()) + $loop->iteration;
    }

    public static function secondsToTime($seconds)
    {
        return CarbonInterval::seconds($seconds)->cascade();
    }

    public static function apiVersion()
    {
        return Session::get('API_VERSION', env('DEFAULT_API_VERSION'));
    }

    public static function identifyClickIdProvider($clickId)
    {
        $clickId = trim($clickId);

        if (preg_match('/^\d+$/', $clickId)) {
            return 'gg-agency';
        }

        if (preg_match('/^[a-z0-9]+,[0-9]+,[0-9]+,[0-9]+$/i', $clickId)) {
            return 'traffic-company';
        }

        $mobipiumFormat1 = '/^\d+_\d+_\d+_\d+[a-z]+_[a-f0-9]+_\d+x\d+x[a-f0-9]+$/i';
        $mobipiumFormat2 = '/^[a-zA-Z0-9_-]{50,}$/';

        if (preg_match($mobipiumFormat1, $clickId) || preg_match($mobipiumFormat2, $clickId)) {
            return 'mobipiumlink';
        }

        return 'uknown';
    }
}


/**
 *
 *      UNUSED , but afraid to delete :)
 *
 */
class Hepler
{

    public static function back($route, $params = [])
    {
        $lastPreviousKnown = URL::previous();
        return $lastPreviousKnown == URL::current()
            ? route($route, $params)
            : $lastPreviousKnown;
    }
    public static function getFilePath($path)
    {
        return Storage::url($path);
    }

    public static function tableIndex($collection, $loop)
    {
        return (($collection->currentPage() - 1) * $collection->perPage()) + $loop->iteration;
    }

    public static function secondsToTime($seconds)
    {
        return CarbonInterval::seconds($seconds)->cascade();
    }
}
