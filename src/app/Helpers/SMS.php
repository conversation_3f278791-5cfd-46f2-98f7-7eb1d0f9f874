<?php

namespace App\Helpers;

class SMS
{

    public static function send($text, $to)
    {
        $HOST = "http://**************/dsms/webacc.aspx?user=test&pwd=tt@123&smstext=helloHTTP&Sender=DataSoft&Nums=249113042009"; // env('SMS_CLIENT_HOST');
        $USER = env('SMS_CLIENT_USER');
        $PASS = env('SMS_CLIENT_PASS');
        $SENDER = env('SMS_CLIENT_SENDER');

        $client = new \GuzzleHttp\Client();

        $response = $client->request('GET', $HOST);

        $statusCode = $response->getStatusCode();

        $content = json_decode($response->getBody(), true);

        return $content;
    }
}