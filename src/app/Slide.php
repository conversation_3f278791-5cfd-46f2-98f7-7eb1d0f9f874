<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Slide extends Model
{
    protected $guarded = ['id'];

    /**
     *---------------------------------------
     *      Relations
     *---------------------------------------
     */

    /**
     *     slidable relation
     */
    public function slidable()
    {
        return $this->morphTo();
    }



    /**
     *---------------------------------------
     *      Functions & Logic
     *---------------------------------------
     */

    /**
     *  Get unique sequence numbers
     */
    public static function getUniqueSequence()
    {
        $lastInSequene = Slide::all()->sortByDesc('sequence')->first();

        if (is_null($lastInSequene)) {
            return 1;
        }

        return  $lastInSequene->sequence ?  $lastInSequene->sequence + 1 : 1;
    }
}