<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class File extends Model
{
    protected $guarded = ['id'];


    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'uuid';
    }

    /**
     *---------------------------------------
     *      Relations
     *---------------------------------------
     */

    /**
     *     file relation
     */
    public function filable()
    {
        return $this->morphTo();
    }
}
