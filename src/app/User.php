<?php

namespace App;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Tymon\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements J<PERSON>TSubject
{
    use Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [
        'id'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     *---------------------------------------
     *      Relations
     *---------------------------------------
     */

    /**
     *     History relation
     */
    public function history()
    {
        return $this->hasMany(RecentlyPlayed::class);
    }

    /**
     *     Like relation
     */
    public function favorites()
    {
        return $this->hasMany(Like::class);
    }

    /**
     *     Like relation
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class);
    }

    /**
     *     Subscription relation
     */
    public function subscription()
    {
        return $this->hasOne(Subscription::class);
    }

    /**
     *     Billing relation
     */
    public function billings()
    {
        return $this->hasMany(Billing::class);
    }

    /**
     *     Latest Billing relation
     */
    public function latest_billing()
    {
        return $this->hasOne(Billing::class)->latest();
    }

    /**
     *     Like relation
     */
    public function likes()
    {
        return $this->hasMany(Like::class);
    }
    
    /**
     *     Preferred Content relation
     */
    public function preferredContent()
    {
        return $this->hasMany(UserContentPreference::class);
    }

    /**
     *     SearchLog relation
     */
    public function searchLogs()
    {
        return $this->hasMany(SearchLog::class);
    }

    /*
     *
     *
     *  Common Functions
     *
     *
     */

    public function hasRole($role_code)
    {
        return $this->roles()->where('code', $role_code)->exists();
    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    public function getIsSubscribedAttribute()
    {
        if (!$this->subscription) {
            return False;
        }
        return $this->subscription->is_subscribed;
    }

    public function getHasActiveBillingAttribute()
    {
        if (!$this->is_subscribed) {
            return False;
        }

        return $this->subscription->active_billing_until > now()->subHours(Subscription::FREE_PREMIUM_HOURS_OFFSET);
    }

    public function getHasActiveTrialAttribute()
    {
        if (!$this->is_subscribed) {
            return False;
        }

        return $this->subscription->created_at > now()->subHours(Subscription::TRIAL_PREMIUM_HOURS_OFFSET);
    }

    public function getActiveTrialUntilAttribute()
    {
        if (!$this->is_subscribed) {
            return null;
        }

        return $this->subscription->created_at->addHours(Subscription::TRIAL_PREMIUM_HOURS_OFFSET);
    }

}
