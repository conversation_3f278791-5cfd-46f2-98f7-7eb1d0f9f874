<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Book extends Model
{
    protected $guarded = ['id'];

    const LANGUAGE_ARABIC = 'ar';
    const LANGUAGE_ENGLISH = 'en';

    const REGION_SUDAN = 'sd';
    const REGION_OTHER = 'other';


    public function getHasEpisodesAttribute()
    {
        return (bool) count($this->episodes);
    }

    /**
     *---------------------------------------
     *      Relations
     *---------------------------------------
     */


    /**
     *     file relation
     */
    public function file()
    {
        return $this->morphOne(File::class, 'filable');
    }

    /**
     *     episodes relation
     */
    public function episodes()
    {
        return $this->hasMany(Episode::class);
    }

    /**
     *     publishing_logs relation
     */
    public function publishing_logs()
    {
        return $this->hasMany(BookPublishingLog::class);
    }

    /**
     *   category relation
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     *     vendor relation
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     *     authors relation
     */
    public function authors()
    {
        return $this->belongsToMany(Author::class);
    }

    /**
     *     Like relation
     */
    public function likes()
    {
        return $this->morphMany(Like::class, 'likable');
    }

    /**
     *     Slide relation
     */
    public function slide()
    {
        return $this->morphOne(Slide::class, 'slidable');
    }
}
