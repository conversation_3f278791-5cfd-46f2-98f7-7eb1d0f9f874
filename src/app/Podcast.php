<?php

namespace App;

use Carbon\Traits\Date;
use DateTime;
use Illuminate\Database\Eloquent\Model;

class Podcast extends Model
{
    protected $guarded = ['id'];


    /**
     *---------------------------------------
     *      Relations
     *---------------------------------------
     */

    /**
     *     file relation
     */
    public function file()
    {
        return $this->morphOne(File::class, 'filable');
    }

    /**
     *     program relation
     */
    public function program()
    {
        return $this->belongsTo(Program::class);
    }

    /**
     *     playlists relation
     */
    public function playlists()
    {
        return $this->belongsToMany(Playlist::class);
    }

    /**
     *     Like relation
     */
    public function likes()
    {
        return $this->morphMany(Like::class, 'likable');
    }


    /**
     *     Like relation
     */
    public function recentPlay()
    {
        return $this->hasMany(RecentlyPlayed::class);
    }
}