<?php

namespace App\Services;

use App\Book;
use App\Category;
use App\UserContentPreference;

class PreferenceService
{
    public static function addBookToPreference(Book $book, $user)
    {
        UserContentPreference::updateOrCreate([
            'user_id' => $user->id,
            'preferable_content_type' => Book::class,
            'preferable_content_id' => $book->id,
        ],[
            'creation_type' => UserContentPreference::CREATION_TYPE_MANUAL,
        ]);
        UserContentPreference::updateOrCreate([
            'user_id' => $user->id,
            'preferable_content_type' => Category::class,
            'preferable_content_id' => $book->category_id,
        ],[
            'creation_type' => UserContentPreference::CREATION_TYPE_RELATED,
        ]);
    }

    public static function removeBookFromPreference(Book $book, $user)
    {
        UserContentPreference::where('user_id', $user->id)->where('preferable_content_type', Book::class)->where('preferable_content_id', $book->id)->delete();
        UserContentPreference::where('user_id', $user->id)->where('preferable_content_type', Category::class)->where('preferable_content_id', $book->category_id)->where('creation_type', UserContentPreference::CREATION_TYPE_RELATED)->delete();
    }
}
