<?php

namespace App\Services;

use App\User;

class UserLogService
{

    public static function logLastActivityDateTime(User $user)
    {
        $user = $user->update(['latest_activity_at' => now() ]);
    }

    public static function logSearchQuery(User $user, $query, $type)
    {
        $user->searchLogs()->create([
            'query' => $query,
            'type' => $type,
        ]);
    }
}
