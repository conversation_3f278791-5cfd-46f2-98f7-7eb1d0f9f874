<?php

namespace App\Services;

use App\Billing;
use App\Subscription;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Str;

class SubscriptionService {

    
    public static function submitBilling(string $subs_phone, $subs_expiry = null, $phone_verified_at = null, $country_code = "sd")
    {
        $user = self::getUser(self::getFormattedPhone($subs_phone, $country_code));

        Subscription::updateOrCreate([
            'user_id' => $user->id,
            'phone' => self::getFormattedPhone($subs_phone, $country_code),
        ],[
            'active_billing_until' => $subs_expiry,
            'phone_verified_at' => $phone_verified_at,
            'operator' => self::getOperatorName($subs_phone, $country_code),
            'is_subscribed' => true,
        ]);
    }

    public static function submitTransaction(string $subs_phone, $expiry_date, $amount = null, $operator = null) {
        if(is_null($expiry_date)) return;
        
        $user = self::getUser(self::getFormattedPhone($subs_phone));

        $transaction_id = self::generateTransactionId($user, $expiry_date);

        Billing::updateOrCreate([
            'transaction_ref' => $transaction_id,
            'user_id' => $user->id,
        ], [
            'amount' => $amount,
            'operator' => request('operator') ?? 'zain',
            'expiry_date' => $expiry_date,
        ]);
    }

    public static function unsubscribe($phone, $country_code = "sd")
    {
        $user = self::getUser(self::getFormattedPhone($phone, $country_code));

        if($user->subscription){
            $subscription = $user->subscription;
            $subscription->is_subscribed = false;
            $subscription->save();
        }
    }

    public static function getUser($phone)
    {
        return User::updateOrCreate([
            'phone' => self::getFormattedPhone($phone),
        ],[
            'email' => 'default.' . date('ymdis') . '.' . rand(100, 1000) . '@digitalplusteq.com',
            'name' => 'default',
            'password' => bcrypt(Str::uuid()),
            'type' => 'Subscriber',
        ]);
    }

    public static function generateTransactionId($user, $expiry_date) {
        return $user->id . '-' . Carbon::parse($expiry_date)->timestamp;
    }

    public static function getFormattedPhone($phone, $country_code = "sd")
    {
        $phone_length = match($country_code) {
            "sd" => -9,
            "eg" => -10,
        };

        $phone = str_replace(' ', '', $phone);
        $phone = str_replace('-', '', $phone);
        $phone = str_replace('+', '', $phone);
        $phone = str_replace('(', '', $phone);
        $phone = str_replace(')', '', $phone);
        $formatted_phone = Str::substr($phone, $phone_length);

        if (strlen($formatted_phone) === 8) { // New Sudani integration
            return "2491" . $formatted_phone;
        }

        return match ($country_code) {
            "sd" => "249" . $formatted_phone,
            "eg" => "20" . $formatted_phone,
        };
    }

    public static function getOperatorName(string $phone, string $country_code = "sd")
    {
        return request('operator') ?? 'zain',
    }
}
