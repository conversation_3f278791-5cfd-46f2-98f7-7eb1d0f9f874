<?php

namespace App\Services;

use App\CachedVariable;
use DateTime;
use Exception;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Log;

class PaymentGateway
{

    const ZAIN_ENCRYPTED_PASSWORD_VARIABLE = 'ZAIN_ENCRYPTED_PASSWORD';
    const ZAIN_TOKEN_VARIABLE = 'ZAIN_TOKEN';
    private $providerKey;
    private $seriveURL;
    private $productCode;
    private $password;
    private $username;
    private $rememberMe;
    private $verifySSL = false;

    private $client;

    public function __construct()
    {
        $this->providerKey = env('ZAIN_PROVIDER_KEY');
        $this->seriveURL = env('ZAIN_SERVER_BASE_API_URL');
        $this->productCode = env('ZAIN_PRODUCT_CODE');
        $this->password = env('ZAIN_PASSWORD');
        $this->username = env('ZAIN_USERNAME');
        $this->rememberMe = env('ZAIN_REMEMBER_TOKEN');

        $this->client = new \GuzzleHttp\Client([
            'base_uri' => $this->seriveURL,
            'timeout' => 60,
        ]);
    }

    public function initialPayment(string $msisdn)
    {
        try {
            $response = $this->client->post('initiate.php', [
                'json' => [
                    'msisdn' => $msisdn,
                    'product_code' => $this->productCode,
                ],
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->getToken(),
                    'Content-Type' => 'application/json',
                ],
                'verify' => $this->verifySSL,
            ]);

            $body = json_decode($response->getBody(), true);
            Log::debug('InitPay');
            Log::debug($body);

            if ($body['success'] == true) {

                $subscribe_request_id = $body['subscribe_request_id'];

                return $subscribe_request_id;
            }
        } catch (Exception $e) {
            return null;
        }

        return null;
    }

    public function verifyPIN(string $pin, $subscribe_request_id)
    {
        try {
            $response = $this->client->post($this->seriveURL . 'payment.php', [
                'json' => [
                    'otp' => $pin,
                    'subscribe_request_id' => $subscribe_request_id,
                ],
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->getToken(),
                    'Content-Type' => 'application/json',
                ],
                'verify' => $this->verifySSL,
            ]);

            $body = json_decode($response->getBody(), true);
            Log::debug('Payment');
            Log::debug($body);

            return $body['success'];

        } catch (Exception $e) {
            return null;
        }

        return null;
    }

    public function checkSubscription(string $msisdn)
    {
        $is_subscribed = false;
        $has_active_billing = false;
        $subDate = null;
        $endSubDate = null;
        $creationDate = null;

        try {
        $response = $this->client->post($this->seriveURL . 'check.php', [
            'json' => [
                'msisdn' => $msisdn,
                'product_code' => $this->productCode,
            ],
            'headers' => [
                'Authorization' => 'Bearer ' . $this->getToken(),
                'Content-Type' => 'application/json',
            ],
            'verify' => $this->verifySSL,
        ]);

        $body = json_decode($response->getBody(), true);
        Log::debug('CheckSubscription');
        Log::debug($body);

        $is_subscribed = $body['success'];

        $subDateUnix = null;
        $endSubDateUnix = null;

        if($is_subscribed) {
            if (isset($body['data'])) {
                $subDateUnix = $body['data']['subscription_data']['subdate_unix'];
                $endSubDateUnix = $body['data']['subscription_data']['unsubdate_unix'];
                $creationDate = $body['data']['subscription_data']['creation_date'];
                $has_active_billing = $body['data']['subscription_data']['is_active'];
            } else {
                $subDateUnix = $body['subscription_data']['subdate_unix'];
                $endSubDateUnix = $body['subscription_data']['unsubdate_unix'];
                $creationDate = $body['subscription_data']['creation_date'];
                $has_active_billing = $body['subscription_data']['is_active'];
            }
        }

        $subDate = !is_null($subDateUnix) ? (new DateTime())->setTimestamp($subDateUnix)->format('Y-m-d H:i:s') : null;
        $endSubDate = !is_null($endSubDateUnix) ? (new DateTime())->setTimestamp($endSubDateUnix)->format('Y-m-d H:i:s') : null;
        } catch (Exception $e) {
            Log::alert('Exception: @CheckSubscription');
            Log::alert($e);
        }

        if(isset($body['error_code']) and $body['error_code'] == 118) {
            $is_subscribed = true;
        }

        return [
            'is_subscribed' => (bool) $is_subscribed,
            'has_active_billing' => (bool) $has_active_billing,
            'subDate' => $subDate,
            'endSubDate' => $endSubDate,
            'creationDate' => $creationDate,
        ];
    }

    public function unsubscribe(string $msisdn)
    {
        try {
            $response = $this->client->post($this->seriveURL . 'cancel.php', [
                'json' => [
                    'msisdn' => $msisdn,
                    'product_code' => $this->productCode,
                ],
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->getToken(),
                    'Content-Type' => 'application/json',
                ],
                'verify' => $this->verifySSL,
            ]);

            $body = json_decode($response->getBody(), true);
            Log::debug('UnSubscribe');
            Log::debug($body);

            return $body['success'];
        } catch (Exception $e) {
            return false;
        }

        return false;
    }

    public function getToken()
    {
        $cachedToken = CachedVariable::where('name', self::ZAIN_TOKEN_VARIABLE)->first();

        if (!is_null($cachedToken) && Date::parse($cachedToken->optional)->subMinutes(1) > now()) {
            return $cachedToken->value;
        } else {
            return $this->getTokenFromSPAY();
        }
    }

    public function getTokenFromSPAY()
    {
        try {
            $response = $this->client->post($this->seriveURL . 'login.php', [
                'json' => [
                    'username' => $this->username,
                    'password' => $this->password,
                    'remember_me' => $this->rememberMe,
                ],
                'verify' => $this->verifySSL,
            ]);

            $body = json_decode($response->getBody(), true);
            Log::debug('Login');
            Log::debug($body);

            if ($body['success'] == true) {

                $token = $body['token'];
                $expireDate = $this->rememberMe ? now()->addDays(29) : now()->addHours(23);
                $this->cacheToken($token, $expireDate);

                return $token;
            }
        } catch (Exception $e) {
            return null;
        }

        return null;
    }

    public function cacheToken($token, $expireDate)
    {
        CachedVariable::updateOrCreate([
            'name' => self::ZAIN_TOKEN_VARIABLE,
        ]
            , [
                'value' => $token,
                'optional' => $expireDate,
                'value_json' => json_encode([
                    'token' => $token,
                    'expireDate' => $expireDate,
                ]),
            ]);
        return $token;
    }

    public function cachePassword($password)
    {
        CachedVariable::updateOrCreate([
            'name' => self::ZAIN_ENCRYPTED_PASSWORD_VARIABLE,
        ]
            , [
                'value' => $password,
                'value_json' => json_encode($password),
            ]);
        return $password;
    }
}
