<?php

namespace App\Services;

class DemoService
{
    public static function allowedUsersPhones() {

        $phones_str = config('demo.allowed_phones', '');
        return empty($phones_str) || is_null($phones_str)
                ? []
                : explode(',', $phones_str);
    }

    public static function userSubStatus() {
        return [
            'is_subscribed' => true,
            'has_active_billing' => true,
            'subDate' => now(),
            'endSubDate' => now()->addDay(),
            'creationDate' => now(),
        ];
    }
}
