<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Program extends Model
{
    protected $guarded = ['id'];

    /**
     *---------------------------------------
     *      Relations
     *---------------------------------------
     */

    /**
     *     category relation
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     *     vendor relation
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     *     podcasts relation
     */
    public function podcasts()
    {
        return $this->hasMany(Podcast::class);
    }

    /**
     *     liveStreams relation
     */
    public function liveStreams()
    {
        return $this->hasMany(LiveStream::class);
    }

    /**
     *     playlists relation
     */
    public function playlists()
    {
        return $this->morphMany(Playlist::class, 'owner');
    }

    /**
     *     podcasts relation
     */
    public function recentPlay()
    {
        return $this->hasManyThrough(RecentlyPlayed::class, Podcast::class);
    }
}