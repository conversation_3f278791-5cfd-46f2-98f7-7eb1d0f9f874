<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    protected $guarded = ['id'];


    /**
     *---------------------------------------
     *      Relations
     *---------------------------------------
     */

    /**
     *     Programs relation
     */
    public function programs()
    {
        return $this->hasMany(Program::class);
    }

    /**
     *     podcasts relation
     */
    public function podcasts()
    {
        return $this->hasManyThrough(Podcast::class, Program::class);
    }

    /**
     *     boks relation
     */
    public function books()
    {
        return $this->hasMany(Book::class);
    }
}
