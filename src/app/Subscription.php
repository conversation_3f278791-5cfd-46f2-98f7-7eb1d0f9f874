<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    protected $guarded = ['id'];

    /**
     * The offset of time to allow subscribers to extend the premium subscription in hours.
     *
     * @var int
     */
    public const FREE_PREMIUM_HOURS_OFFSET = 24;

    /**
     * The offset of time to allow new subscribers to use premium subscription as trial, per hour.
     *
     * @var int
     */
    public const TRIAL_PREMIUM_HOURS_OFFSET = 24;


    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'active_billing_until' => 'datetime',
        'phone_verified_at' => 'datetime',
    ];

}
