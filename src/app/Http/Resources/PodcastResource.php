<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class PodcastResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            "url" => Storage::url(!is_null($this->file) ? $this->file->url : ($this->url)),
            "program" => PodcastResource::make($this->program),
            "name_ar" => $this->name_ar,
            "name_en" => $this->name_en,
            "thumbnail" => Storage::url($this->thumbnail),
            "short_description_ar" => $this->short_description_ar,
            "short_description_en" => $this->short_description_en,
            "full_description_ar" => $this->full_description_ar,
            "full_description_en" => $this->full_description_en,
            "like_id" => $this->liked,
            "is_premium" => boolval($this->is_premium),
            "created_at" => $this->created_at->diffForHumans(null, true, true),
        ];
    }
}
