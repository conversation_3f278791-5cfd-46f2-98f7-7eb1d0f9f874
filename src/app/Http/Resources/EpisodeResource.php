<?php

namespace App\Http\Resources;

use App\Helpers\Helper;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class EpisodeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            "url" => Storage::url($this->file->url),
            "title_ar" => $this->title_ar,
            "title_en" => $this->title_en,
            "episode_number" => $this->episode_number,
            "created_at" => $this->created_at,
        ];
    }
}
