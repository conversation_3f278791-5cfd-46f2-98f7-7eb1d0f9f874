<?php

namespace App\Http\Resources;

use App\Services\DemoService;
use Illuminate\Http\Resources\Json\JsonResource;

class DemoUserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $demoSubStatus = DemoService::userSubStatus();
        return [
            'id' => $this->id,
            // "name" => $this->name,
            // "email" => $this->email,
            // "type" => $this->type,
            'phone' => $this->phone,
            'latest_activity_at' => $this->latest_activity_at,
            'created_at' => $demoSubStatus['creationDate'],
            'is_subscribed' => $demoSubStatus['is_subscribed'],
            'has_active_billing' => $demoSubStatus['has_active_billing'],
            'active_billing_until' => $demoSubStatus['endSubDate'],
            'has_active_trial' => (bool) $this->has_active_trial,
            'active_trial_until' => $this->active_trial_until,
        ];
    }
}
