<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class SlideResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            "book" => BookResource::make($this->slidable),
            // 'slidable' => $this->slidable,
            "url" => Storage::url(!is_null($this->cover) ? $this->cover : $this->slidable->slide_cover),
            "label_ar" => $this->slidable->name_ar,
            "label_en" => $this->slidable->name_en,
            "slidable_id" => $this->slidable_id,
            "slidable_type" => $this->slidable_type,
            "short_description_ar" => $this->short_description_ar,
            "short_description_en" => $this->short_description_en,
        ];
    }
}
