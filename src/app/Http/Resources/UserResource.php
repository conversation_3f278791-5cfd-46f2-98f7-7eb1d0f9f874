<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            // "name" => $this->name,
            // "email" => $this->email,
            // "type" => $this->type,
            "phone" => $this->phone,
            "created_at" => $this->created_at,
            "latest_activity_at" => $this->latest_activity_at,
            "is_subscribed" => (bool) $this->is_subscribed,
            "has_active_billing" => (bool) $this->has_active_billing,
            "active_billing_until" => $this->subscription->active_billing_until ?? null,
            "has_active_trial" => (bool) $this->has_active_trial,
            "active_trial_until" => $this->active_trial_until,
        ];
    }
}
