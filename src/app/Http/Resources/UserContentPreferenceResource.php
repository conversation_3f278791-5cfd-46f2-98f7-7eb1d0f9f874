<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserContentPreferenceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name_ar' => isset($this->preferableContent->title_ar) ? $this->preferableContent->title_ar : $this->preferableContent->name_ar,
            'name_en' => isset($this->preferableContent->title_en) ? $this->preferableContent->title_en : $this->preferableContent->name_en,
            'type' => $this->preferable_content_type,
            'creation_type' => $this->creation_type,
        ];
    }
}
