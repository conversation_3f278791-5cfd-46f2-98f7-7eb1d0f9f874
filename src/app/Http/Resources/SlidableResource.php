<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class SlidableResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            "thumbnail" => Storage::url($this->thumbnail),
            "cover" => Storage::url($this->cover),
            "slide_cover" => Storage::url($this->slide_cover),
            "name_ar" => $this->name_ar,
            "name_en" => $this->name_en,
            "description_ar" => $this->description_ar,
            "description_en" => $this->description_en,
            "url" => Storage::url(!is_null($this->file) ? $this->file->url : ($this->url)),
            "program" => ProgramResource::make($this->program),
            "short_description_ar" => $this->short_description_ar,
            "short_description_en" => $this->short_description_en,
            "full_description_ar" => $this->full_description_ar,
            "full_description_en" => $this->full_description_en,
            "like_id" => $this->liked,
            "created_at" => $this->created_at->diffForHumans(null, true, true),
            "is_live_streaming" => (bool) $this->is_live_stream_program,
            "liveStream" => LiveStreamResource::make($this->liveStreams()->latest()->first()),
            "playlists" => PlaylistResource::collection($this->playlists)
        ];
    }
}