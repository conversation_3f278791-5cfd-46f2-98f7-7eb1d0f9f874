<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class DynamicPageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "uuid" => $this->uuid,
            "is_visible" => (bool) $this->is_visible,
            "title_ar" => $this->title_ar,
            "title_en" => $this->title_en,
            "content_ar" => $this->content_ar,
            "content_en" => $this->content_en,
        ];
    }
}
