<?php

namespace App\Http\Resources;

use App\Helpers\Helper;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class BookResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            "title_ar" => $this->title_ar,
            "title_en" => $this->title_en,
            "cover" => $this->cover ? Storage::url($this->cover): null,
            "pdf" => Helper::getSecureFileURL($this->file),
            "downloads" => $this->downloads,
            "description_ar" => $this->description_ar,
            "description_en" => $this->description_en,
            "publishing_year" => $this->publishing_year,
            "isbn" => $this->isbn,
            "pages" => $this->pages,
            "is_premium" => (bool) $this->is_premium,
            "country" => $this->country,
            "language" => $this->language,
            "views" => $this->views,
            "has_episodes" => $this->has_episodes,
            "created_at" => $this->created_at,
            "category" => $this->category,
            "episodes" => EpisodeResource::collection($this->episodes),
            "authors" => AuthorResource::collection($this->authors),
            "vendor" => $this->vendor,
        ];
    }
}
