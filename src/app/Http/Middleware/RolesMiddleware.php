<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class RolesMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @param $roles
     * @return mixed
     */
    public function handle($request, Closure $next, $roles)
    {


        // Check Authentication First

        if (!Auth::check()) {
            return redirect('/');
        }


        // Check Authorization

        $roles = explode('|', $roles);

        $admin = Auth::user();

        foreach ($roles as $role) {

            if (!$admin->hasRole($role)) {
                return redirect()
                    ->route('dashboard')
                    ->with('error', 'دخول غير مصرح به');
            }
        }

        return $next($request);
    }
}