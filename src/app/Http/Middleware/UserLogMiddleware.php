<?php

namespace App\Http\Middleware;

use App\Services\UserLogService;
use Closure;

class UserLogMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {

        if(auth('api')->check()) {
            UserLogService::logLastActivityDateTime(auth('api')->user());
        }

        return $next($request);
    }
}
