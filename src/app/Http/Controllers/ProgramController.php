<?php

namespace App\Http\Controllers;

use App\Category;
use App\Helpers\Image;
use App\Program;
use App\Slide;
use App\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ProgramController extends Controller
{
    public function __construct()
    {
        $this->middleware('authorize:manage_programs');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $programs = Program::latest()->paginate(10);

        return view('panel.program.index', compact('programs'));
    }

    /**
     *  Latest list of Programs json response
     * @param Slide
     * @return Slide
     */
    public function indexJson(Request $request)
    {
        return response(Program::latest()->get());
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('panel.program.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'thumbnail' => 'required|image|mimes:' . Image::uploadableImageExtension(),
            'cover' => 'required|image|mimes:' . Image::uploadableImageExtension(),
            'name_ar' => 'required|string|max:191',
            'name_en' => 'required|string|max:191',
            'category' => 'required|integer',
            'vendor' => 'required|integer',
            'is_visible' => 'required|boolean',
        ]);

        $program = new Program([
            'thumbnail' => $request->file('thumbnail')->store('public/programs/' . date('Y/m')),
            'cover' => $request->file('cover')->store('public/programs/' . date('Y/m')),
            'name_ar' => $request->post('name_ar'),
            'name_en' => $request->post('name_en'),
            'category_id' => Category::findOrFail($request->post('category'))->id, // Check befor insert trick
            'vendor_id' => Vendor::findOrFail($request->post('vendor'))->id, // Check befor insert trick
            'description_ar' => $request->post('description_ar'),
            'description_en' => $request->post('description_en'),
            'is_live_stream_program' => $request->post('is_live_stream_program') ?: 0,
            'is_visible' => $request->post('is_visible'),
        ]);


        // Check if has silde cover
        if ($request->hasFile('slide_cover')) {
            $request->validate([
                'slide_cover' => 'required|image|mimes:' . Image::uploadableImageExtension(),
            ]);

            // upload slide cover
            $program->slide_cover = $request->file('slide_cover')->store('public/programs/' . date('Y/m'));

            // Save
            $program->save();

        } else {
            // Just Save
            $program->save();
        }


        return redirect()->route('program.index')->with('success', 'تمت الإضافة بنجاح');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Program  $program
     * @return \Illuminate\Http\Response
     */
    public function show(Program $program)
    {
        $podcasts = $program->podcasts()->paginate(10);
        return view('panel.program.show', compact('program', 'podcasts'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Program  $program
     * @return \Illuminate\Http\Response
     */
    public function edit(Program $program)
    {
        return view('panel.program.edit', compact('program'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Program  $program
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Program $program)
    {
        $request->validate([
            'name_ar' => 'required|string|max:191',
            'name_en' => 'required|string|max:191',
            'category' => 'required|integer',
            'vendor' => 'required|integer',
            'is_visible' => 'required|boolean',
        ]);

        // Thumbnail
        if ($request->hasFile('thumbnail')) {
            $request->validate([
                'thumbnail' => 'required|image|mimes:' . Image::uploadableImageExtension(),
            ]);

            // Delete old one
            Storage::delete($program->thumbnail);

            // Upload new one
            $program->thumbnail = $request->file('thumbnail')->store('public/programs/' . date('Y/m'));
        }

        // Cover Image
        if ($request->hasFile('cover')) {
            $request->validate([
                'cover' => 'required|image|mimes:' . Image::uploadableImageExtension(),
            ]);

            // Delete old one
            Storage::delete($program->cover);

            // Upload new one
            $program->cover = $request->file('cover')->store('public/programs/' . date('Y/m'));
        }

        // Slider Cover Image
        if ($request->hasFile('slide_cover')) {
            $request->validate([
                'slide_cover' => 'required|image|mimes:' . Image::uploadableImageExtension(),
            ]);

            // Delete old one
            Storage::delete($program->slide_cover);

            // Upload new one
            $program->slide_cover = $request->file('slide_cover')->store('public/programs/' . date('Y/m'));
        }

        $program->name_ar = $request->post('name_ar');
        $program->name_en = $request->post('name_en');
        $program->description_ar = $request->post('description_ar');
        $program->description_en = $request->post('description_en');
        $program->category_id = Category::findOrFail($request->post('category'))->id; // Check befor insert trick
        $program->vendor_id = Vendor::findOrFail($request->post('vendor'))->id; // Check befor insert trick
        $program->is_live_stream_program = $request->post('is_live_stream_program') ?: 0;
        $program->is_visible = $request->post('is_visible');

        $program->save();

        return redirect()->back()->with('success', 'تم تعديل البيانات بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Program  $program
     * @return \Illuminate\Http\Response
     */
    public function destroy(Program $program)
    {
        // Delete files first
        Storage::delete($program->thumbnail);       // thumbnail
        Storage::delete($program->cover);           // Cover Image
        Storage::delete($program->slide_cover);     // Slide cover

        // Delete record
        $program->delete();

        return redirect()->back()->with('success', 'تم مسح البرنامج وعرض الشرائح الخاص به بنجاح!');
    }
}
