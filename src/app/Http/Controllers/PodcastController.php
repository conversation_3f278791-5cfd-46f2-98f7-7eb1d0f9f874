<?php

namespace App\Http\Controllers;

use App\Album;
use App\Category;
use App\File;
use App\Helpers\Image;
use App\Podcast;
use App\Poet;
use App\Program;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class PodcastController extends Controller
{
    public function __construct()
    {
        $this->middleware('authorize:manage_podcasts');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $podcasts = Podcast::latest()->paginate(10);

        return view('panel.podcast.index', compact('podcasts'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('panel.podcast.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'thumbnail' => 'required|image|mimes:' . Image::uploadableImageExtension(),
            'audio' => 'required|file',
            'program' => 'required|integer',
            'name_ar' => 'required|string|max:191',
            'name_en' => 'required|string|max:191',
            'short_description_ar' => 'required|string|max:191',
            'short_description_en' => 'required|string|max:191',
        ]);

        // Create file record
        $file = new File([
            'uuid' => Str::uuid(),
            'url' => $request->file('audio')->store('public/audios/' . date('Y/m')),
            'size' => $request->file('audio')->getSize(),
            'length' => '0',
        ]);

        // Create podcast record
        $podcast = Podcast::create([
            'program_id' => Program::findOrFail($request->post('program'))->id, // Double check befor injecting id
            'thumbnail' => $request->file('thumbnail')->store('public/podcasts/' . date('Y/m')),
            'name_ar' => $request->post('name_ar'),
            'name_en' => $request->post('name_en'),
            'short_description_ar' => $request->post('short_description_ar'),
            'short_description_en' => $request->post('short_description_en'),
            'full_description_ar' => $request->post('full_description_ar'),
            'full_description_en' => $request->post('full_description_en'),
            'is_premium' => $request->post('is_premium') ?? FALSE,
            'approved_at' => Date::today(),
        ]);

        $podcast->file()->save($file);

        return redirect()->route('podcast.index')->with('success', 'تمت إضافة الملف بنجاح');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Podcast  $podcast
     * @return \Illuminate\Http\Response
     */
    public function show(Podcast $podcast)
    {
        return view('panel.podcast.show', compact('podcast'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Podcast  $podcast
     * @return \Illuminate\Http\Response
     */
    public function edit(Podcast $podcast)
    {
        return view('panel.podcast.edit', compact('podcast'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Podcast  $podcast
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Podcast $podcast)
    {
        $v = validator($request->all(), [
            'program' => 'required|integer',
            'name_ar' => 'required|string|max:191',
            'name_en' => 'required|string|max:191',
            'short_description_ar' => 'required|string|max:191',
            'short_description_en' => 'required|string|max:191',
        ]);

        if ($v->fails()) {
            // dd($v->errors());
            return redirect()->back()->withErrors($v->errors());
        }

        // Update audio file if exist
        if ($request->hasFile('audio')) {
            $request->validate([
                'audio' => 'required|file',
            ]);

            // Delete old audio file
            Storage::delete($podcast->file->url);

            // Upload new file & Update filable record
            $podcast->file()->update([
                'url' => $request->file('audio')->store('public/audios/' . date('Y/m')),
                'size' => $request->file('audio')->getSize(),
                'length' => '0',
            ]);
        }

        // Update thumbnail if exist
        if ($request->hasFile('thumbnail')) {
            $request->validate([
                'thumbnail' => 'required|image|mimes:' . Image::uploadableImageExtension(),
            ]);

            // Delete old thumbnail file
            Storage::delete($podcast->thumbnail);

            // Upload & Update new one
            $podcast->update(['thumbnail' => $request->file('thumbnail')->store('public/podcasts/' . date('Y/m'))]);
        }

        // Update podcast other feilds
        $podcast->update([
            'program_id' => Program::findOrFail($request->post('program'))->id, // Double check befor injecting id
            'name_ar' => $request->post('name_ar'),
            'name_en' => $request->post('name_en'),
            'short_description_ar' => $request->post('short_description_ar'),
            'short_description_en' => $request->post('short_description_en'),
            'full_description_ar' => $request->post('full_description_ar'),
            'full_description_en' => $request->post('full_description_en'),
            'is_premium' => $request->post('is_premium') ?? FALSE,
        ]);

        return redirect()->back()->with('success', 'تم تعديل بيانات الملف بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Podcast  $podcast
     * @return \Illuminate\Http\Response
     */
    public function destroy(Podcast $podcast)
    {
        // Delete files first
        Storage::delete($podcast->thumbnail);
        Storage::delete($podcast->file->url);
        $podcast->file()->delete(); // must delete App\File record

        // Delete record
        $podcast->delete();

        return redirect()->back()->with('success', 'تم مسح الملف ومعلوماته بنجاح!');
    }
}
