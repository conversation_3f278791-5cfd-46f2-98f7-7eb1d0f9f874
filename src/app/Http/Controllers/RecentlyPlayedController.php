<?php

namespace App\Http\Controllers;

use App\RecentlyPlayed;
use Illuminate\Http\Request;

class RecentlyPlayedController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\RecentlyPlayed  $recentlyPlayed
     * @return \Illuminate\Http\Response
     */
    public function show(RecentlyPlayed $recentlyPlayed)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\RecentlyPlayed  $recentlyPlayed
     * @return \Illuminate\Http\Response
     */
    public function edit(RecentlyPlayed $recentlyPlayed)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\RecentlyPlayed  $recentlyPlayed
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, RecentlyPlayed $recentlyPlayed)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\RecentlyPlayed  $recentlyPlayed
     * @return \Illuminate\Http\Response
     */
    public function destroy(RecentlyPlayed $recentlyPlayed)
    {
        //
    }
}
