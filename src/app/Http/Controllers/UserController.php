<?php

namespace App\Http\Controllers;

use App\Services\PaymentGateway;
use App\User;
use Illuminate\Http\Request;

class UserController extends Controller
{

    public function __construct()
    {
        $this->middleware('authorize:manage_users');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $DEFAULT_FROM_DATE = '1000-01-01T00:00:01.01Z';
        $DEFAULT_TO_DATE = now()->addYear();

        $subscriptionFromDate = $request->has('subscriptionFromDate') && !empty($request->get('subscriptionFromDate')) ? $request->get('subscriptionFromDate') : $DEFAULT_FROM_DATE;
        $subscriptionToDate = $request->has('subscriptionToDate') && !empty($request->get('subscriptionToDate')) ? $request->get('subscriptionToDate') : $DEFAULT_TO_DATE;
        $subscriptionFromTime = $request->has('subscriptionFromTime') && !empty($request->get('subscriptionFromTime')) ? $request->get('subscriptionFromTime') : $DEFAULT_FROM_DATE;
        $subscriptionToTime = $request->has('subscriptionToTime') && !empty($request->get('subscriptionToTime')) ? $request->get('subscriptionToTime') : $DEFAULT_TO_DATE;

        $billingFromDate = $request->has('billingFromDate') && !empty($request->get('billingFromDate')) ? $request->get('billingFromDate') : $DEFAULT_FROM_DATE;
        $billingToDate = $request->has('billingToDate') && !empty($request->get('billingToDate')) ? $request->get('billingToDate') : $DEFAULT_TO_DATE;
        $billingFromTime = $request->has('billingFromTime') && !empty($request->get('billingFromTime')) ? $request->get('billingFromTime') : $DEFAULT_FROM_DATE;
        $billingToTime = $request->has('billingToTime') && !empty($request->get('billingToTime')) ? $request->get('billingToTime') : $DEFAULT_TO_DATE;

        $userPhone = $request->userPhone;
        $subscriptionPhone = $request->subscriptionPhone;

        $query = User::with(['subscription', 'latest_billing'])
            ->where('type', 'Subscriber')
            ->where('phone', 'LIKE', "%${userPhone}%");

        if ($subscriptionFromDate != $DEFAULT_FROM_DATE || $subscriptionToDate != $DEFAULT_TO_DATE || $subscriptionFromTime != $DEFAULT_FROM_DATE || $subscriptionToTime != $DEFAULT_TO_DATE || !empty($subscriptionPhone)) {
            $query = $query->whereHas('subscription', function ($query) use ($subscriptionFromDate, $subscriptionToDate, $subscriptionFromTime, $subscriptionToTime, $subscriptionPhone) {
                $query->whereDate('created_at', '>=', $subscriptionFromDate)
                ->whereDate('created_at', '<=', $subscriptionToDate)
                ->whereTime('created_at', '>=', $subscriptionFromTime)
                ->whereTime('created_at', '<=', $subscriptionToTime)
                ->where('phone', 'LIKE', "%${subscriptionPhone}%");
            });
        }

        if ($billingFromDate != $DEFAULT_FROM_DATE || $billingToDate != $DEFAULT_TO_DATE || $billingFromTime != $DEFAULT_FROM_DATE || $billingToTime != $DEFAULT_TO_DATE) {
            $query = $query->whereHas('latest_billing', function ($query) use ($billingFromDate, $billingToDate, $billingFromTime, $billingToTime) {
                $query->whereDate('created_at', '>=', $billingFromDate)
                ->whereDate('created_at', '<=', $billingToDate)
                ->whereTime('created_at', '>=', $billingFromTime)
                ->whereTime('created_at', '<=', $billingToTime);
            });
        }

        $users = $query->latest()
            ->paginate(25);

        return view('panel.subscription.index', compact('users'));
    }

    public function unsubscribe(Request $request, User $user)
    {
        $phone = $user->subscription->phone ?? null;

        $paymentGatway = new PaymentGateway();
        $status = $paymentGatway->unsubscribe($phone);

        if(!$status) {
            return back()->with('error', 'فشل إلغاء الإشتراك');
        }

        $user->subscription()->update([
            'is_subscribed' => false,
        ]);

        return back()->with('success', 'تم إلغاء الإشتراك بنجاح');
    }
}
