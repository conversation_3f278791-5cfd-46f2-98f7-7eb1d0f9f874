<?php

namespace App\Http\Controllers;

use App\Author;
use App\Book;
use App\Category;
use App\Episode;
use App\File;
use App\Helpers\Image;
use App\Services\BookService;
use App\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BookController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $books = Book::latest()->paginate(10);

        return view('panel.book.index', compact('books'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $categories = Category::orderBy('name_ar')->get();
        $authors = Author::orderBy('name')->get();
        $vendors = Vendor::orderBy('name')->get();

        return view('panel.book.create', compact('categories', 'authors', 'vendors'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'cover' => 'required|image|mimes:' . Image::uploadableImageExtension(),
            'pdf_file' => 'required|file|mimes:pdf',
            'title_ar' => 'required|string|max:191',
            'title_en' => 'required|string|max:191',
            'author' => 'required|array',
            'authors.*' => 'required|integer|exists:authors,id',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'pages' => 'required|integer|min:1',
            'isbn' => 'nullable|string',
            'publishing_year' => 'required|integer',
            'category' => 'required|integer|exists:categories,id',
            'publisher' => 'required|integer|exists:vendors,id',
            'country' => 'required|string',
            'language' => 'required|string',
            'is_premium' => 'nullable|boolean',

            // 'episode_number' => 'required|integer|min:1',
            'episode_title_ar' => 'required_with:episode_audio_file|string|nullable|max:191',
            'episode_title_en' => 'required_with:episode_audio_file|string|nullable|max:191',
            'episode_audio_file' => 'required_with:episode_title_ar|file',
        ]);

        DB::transaction(function () use ($request) {

            $book = Book::create([
                'cover' => $request->file('cover')->store('public/books/covers'),
                'title_ar' => $request->title_ar,
                'title_en' => $request->title_en,
                'description_ar' => $request->description_ar,
                'description_en' => $request->description_en,
                'pages' => $request->pages,
                'isbn' => $request->isbn,
                'category_id' => $request->category,
                'vendor_id' => $request->publisher,
                'publishing_year' => $request->publishing_year,
                'country' => $request->country,
                'language' => $request->language,
                'is_premium' => $request->is_premium ?? false,
                'is_published' => true,
            ]);

            $book->authors()->attach($request->author);

            $pdf_file = new File([
                'uuid' => Str::uuid(),
                'url' => $request->file('pdf_file')->store('public/books/pdfs/' . date('Y/m')),
                'size' => $request->file('pdf_file')->getSize(),
                'length' => '0',
            ]);

            $book->file()->save($pdf_file);

            if($request->has('episode_audio_file')) {

                $episode = Episode::create([
                    // 'episode_number' => $request->episode_number,
                    'title_ar' => $request->episode_title_ar,
                    'title_en' => $request->episode_title_en,
                ]);
                $book->episodes()->save($episode );

                $audio_file = new File([
                    'uuid' => Str::uuid(),
                    'url' => $request->file('episode_audio_file')->store('public/books/audios/' . date('Y/m')),
                    'size' => $request->file('episode_audio_file')->getSize(),
                    'length' => '0',
                ]);

                $episode->file()->save($audio_file);
            }

            // Logging
            BookService::logPublishingChanged($book);
        });


        return redirect()->route('book.index')->with([
            'success' => 'تم إضافة الكتاب بنجاح',
            'success' => 'تم إضافة الحلقة الاولى بنجاح',
            ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Book  $book
     * @return \Illuminate\Http\Response
     */
    public function show(Book $book)
    {
        $episodes = $book->episodes;
        $publishingLogs = $book->publishing_logs()->latest()->get();
        return view('panel.book.show', compact('book', 'episodes', 'publishingLogs'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Book  $book
     * @return \Illuminate\Http\Response
     */
    public function edit(Book $book)
    {
        $categories = Category::orderBy('name_ar')->get();
        $authors = Author::orderBy('name')->get();
        $vendors = Vendor::orderBy('name')->get();

        return view('panel.book.edit', compact('book', 'categories', 'authors', 'vendors'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Book  $book
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Book $book)
    {
        $request->validate([
            'cover' => 'nullable|image|mimes:' . Image::uploadableImageExtension(),
            'pdf_file' => 'nullable|file|mimes:pdf',
            'title_ar' => 'required|string|max:191',
            'title_en' => 'required|string|max:191',
            'author' => 'required|array',
            'authors.*' => 'required|integer|exists:authors,id',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'pages' => 'required|integer|min:1',
            'isbn' => 'nullable|string',
            'publishing_year' => 'required|date_format:Y',
            'category' => 'required|integer|exists:categories,id',
            'publisher' => 'required|integer|exists:vendors,id',
            'country' => 'required|string',
            'language' => 'required|string',
            'is_premium' => 'nullable|boolean',
        ]);

        $book->update([
            'title_ar' => $request->title_ar,
            'title_en' => $request->title_en,
            'description_ar' => $request->description_ar,
            'description_en' => $request->description_en,
            'pages' => $request->pages,
            'isbn' => $request->isbn,
            'category_id' => $request->category,
            'vendor_id' => $request->publisher,
            'publishing_year' => $request->publishing_year,
            'country' => $request->country,
            'language' => $request->language,
            'is_premium' => $request->is_premium ?? false,
        ]);

        $book->authors()->sync($request->author);

        if ($request->hasFile('cover')) {
            Storage::delete($book->cover);
            $book->cover = $request->file('cover')->store('public/books/covers');
            $book->save();
        }

        if ($request->hasFile('pdf_file')) {
            Storage::delete($book->file->url);
            $book->file()->update([
                    'url' => $request->file('pdf_file')->store('public/books/pdfs/' . date('Y/m')),
                    'size' => $request->file('pdf_file')->getSize(),
                ]);
        }

        return redirect()->route('book.index')->with(['success' => 'تم تحديث بيانات الكتاب بنجاح']);
    }

    /**
     * Toggle publishing resource for public.
     *
     * @param  \App\Book  $book
     * @return \Illuminate\Http\Response
     */
    public function togglePublishing(Book $book)
    {
        $book->is_published = !$book->is_published;
        $book->save();

        // Logging
        BookService::logPublishingChanged($book);

        $message = '';

        if ($book->is_published) {
            $message = 'تم نشر الكتاب بنجاح!';
        } else {
            $message = 'تم إلغاء نشر الكتاب بنجاح!';
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Book  $book
     * @return \Illuminate\Http\Response
     */
    public function destroy(Book $book)
    {
        if ($book->episodes()->count()) {
            return back()->with('warning', 'يجب حذف الحلقات الخاصة بهذا الكتاب اولا');
        }

        // Delete files first
        Storage::delete($book->cover);
        Storage::delete($book->file->url);
        $book->file()->delete(); // must delete App\File record

        // Delete Slide record
        if (!is_null($book->slide)) {
            $book->slide()->delete();
        }
        // Delete record
        $book->delete();

        return redirect()->back()->with('success', 'تم مسح الملف ومعلوماته بنجاح!');
    }
}
