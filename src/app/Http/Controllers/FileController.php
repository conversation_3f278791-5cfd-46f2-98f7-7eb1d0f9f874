<?php

namespace App\Http\Controllers;

use App\File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FileController extends Controller
{
    /**
     * Download file from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\File  $file
     * @return \Illuminate\Http\Response
     */
    public function download(Request $request, File $file)
    {
        $this->checkAuthority($request, $file);

        return Storage::download($file->url, null, ['Accept-Ranges' => 'bytes']);
    }

    /**
     * Check authority to access this file.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\File  $file
     * @return \Illuminate\Http\Response
     */
    public function checkAuthority(Request $request, File $file)
    {

        // if(
        //     $request->headers->get('xy-client-app') !== env('DOWNLOAD_SECRET')
        //     ) {
        //     abort(404);
        // }

        // if ($file->filable->is_premium && !auth('api')->check()) {
        //     abort(404);
        // }
    }
}
