<?php

namespace App\Http\Controllers;

use App\Playlist;
use App\Podcast;
use App\Program;
use Illuminate\Http\Request;

class PlaylistController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Program $program)
    {
        return view('panel.playlist.create', compact('program'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, Program $program)
    {
        $request->validate([
            'name_ar' => 'required|string|max:191',
            'name_en' => 'required|string|max:191',
        ]);

        Playlist::create([
            'name_ar' => $request->post('name_ar'),
            'name_en' => $request->post('name_en'),
            'owner_id' => $program->id,
            'owner_type' => Program::class,
            'type' => 'for-program',
        ]);

        return redirect()->route('program.show', $program)->with('success', 'تم إنشاء قائمة تشغيل جديدة');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Playlist  $playlist
     * @return \Illuminate\Http\Response
     */
    public function show(Program $program, Playlist $playlist)
    {
        return view('panel.playlist.show', compact('program', 'playlist'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Playlist  $playlist
     * @return \Illuminate\Http\Response
     */
    public function edit(Program $program, Playlist $playlist)
    {
        return view('panel.playlist.edit', compact('program', 'playlist'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Playlist  $playlist
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Program $program, Playlist $playlist)
    {
        $request->validate([
            'name_ar' => 'required|string|max:191',
            'name_en' => 'required|string|max:191',
        ]);

        $playlist->update([
            'name_ar' => $request->post('name_ar'),
            'name_en' => $request->post('name_en'),
        ]);

        return redirect()->back()->with('success', 'تم تحديث بيانات قائمة التشغيل بنجاح');
    }

    /**
     * Show the form for adding podcasts to playlist.
     *
     * @return \Illuminate\Http\Response
     */
    public function add(Program $program, Playlist $playlist)
    {
        return view('panel.playlist.add', compact('program', 'playlist'));
    }

    /**
     * Store a newly added podcasts to playlist.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function attach(Request $request, Program $program, Playlist $playlist)
    {
        $validator = validator($request->all(), [
            'podcasts' => 'array'
        ]);

        if ($validator->fails()) {
            return response(['status' => 'error', 'message' => $validator->errors()[0]]);
        }

        $podcastIDs = $request->post('podcasts');
        $alreadyAttachedIds = $playlist->podcasts()->pluck('podcast_id')->toArray();

        // return $alreadyAttachedIds;

        foreach ($podcastIDs as $podcastID) {
            if (!in_array($podcastID, $alreadyAttachedIds)) {
                $playlist->podcasts()->attach($podcastID);
            }
        }

        return response(['status' => 'success', 'message' => 'تم حفظ الملفات المختارة إلى القائمة']);
    }

    /**
     * Delete podcast from playlist.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function detach(Request $request, Playlist $playlist, Podcast $podcast)
    {

        $playlist->podcasts()->detach($podcast->id);

        return response(['status' => 'success', 'message' => 'تم حذف الملف من القائمة']);
    }

    /**
     * Search in podcast to show them while adding podcast to playlist.
     *
     * @return \Illuminate\Http\Response
     */
    public function searchPodcastsJson(Request $request)
    {
        $program = $request->has('program') ? $request->get('program') : '%%';
        $podcast = $request->has('podcast') ? $request->get('podcast') : '';

        $podcasts = Podcast::with('playlists')
            ->where('program_id', 'LIKE', $program)
            ->where(function ($q) use ($podcast) {
                $q->where('name_ar', 'LIKE', '%' . $podcast . '%');
                $q->orWhere('name_en', 'LIKE', '%' . $podcast . '%');
            })

            ->get();

        return $podcasts;
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Playlist  $playlist
     * @return \Illuminate\Http\Response
     */
    public function destroy(Program $program, Playlist $playlist)
    {
        $playlist->podcasts()->detach();
        $playlist->delete();

        return redirect()->route('program.show', $program)->with('success', 'تم حذف قائمة التشغيل بنجاح');
    }
}