<?php

namespace App\Http\Controllers;

use App\Vendor;
use Illuminate\Http\Request;

class VendorController extends Controller
{
    public function __construct()
    {
        $this->middleware('authorize:manage_vendors');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $vendors = Vendor::latest()->withCount('books')->paginate(10);

        return view('panel.publisher.index', compact('vendors'));
    }

    /**
     *  Latest vendors json response
     */
    public function indexJson()
    {
        $vendors = Vendor::latest()->get();
        return response($vendors);
    }

    /**
     *  Books of specific Vendor
     */
    public function vendorBooksJson(Vendor $vendor)
    {
        return response($vendor->books);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('panel.publisher.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:191',
        ]);

        Vendor::create([
            'name' => $request->post('name'),
        ]);

        return redirect()->route('vendor.index')->with('success', 'تم إضافة دار النشر بنجاح');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Vendor  $vendor
     * @return \Illuminate\Http\Response
     */
    public function show(Vendor $vendor)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Vendor  $vendor
     * @return \Illuminate\Http\Response
     */
    public function edit(Vendor $vendor)
    {
        return view('panel.publisher.edit', compact('vendor'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Vendor  $vendor
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Vendor $vendor)
    {
        $request->validate([
            'name' => 'required|string|max:191',
        ]);

        $vendor->update([
            'name' => $request->post('name'),
        ]);

        return redirect()->back()->with('success', 'تم تحديث دار النشر بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Vendor  $vendor
     * @return \Illuminate\Http\Response
     */
    public function destroy(Vendor $vendor)
    {
        if ($vendor->programs()->count() > 0) {
            return redirect()->back()->with('warning', 'غير قادر على حذف دار النشر، تأكد من نقل البرامج الخاصة به');
        }
        // Delete record
        $vendor->delete();

        return redirect()->back()->with('success', 'تم المسح بنجاح!');
    }
}
