<?php

namespace App\Http\Controllers;

use App\Author;
use Illuminate\Http\Request;

class AuthorController extends Controller
{
    public function __construct()
    {
        $this->middleware('authorize:manage_authors');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $authors = Author::latest()->paginate(10);

        return view('panel.author.index', compact('authors'));
    }

    /**
     *  Latest authors json response
     */
    public function indexJson()
    {
        $authors = Author::latest()->get();
        return response($authors);
    }

    /**
     *  Books of specific Author
     */
    public function authorBooksJson(Author $author)
    {
        return response($author->books);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('panel.author.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:191',
            'description' => 'nullable|string',
            'is_visible' => 'required|boolean',
        ]);

        Author::create([
            'name' => $request->post('name'),
            'description' => $request->post('description'),
            'is_visible' => $request->post('is_visible'),
        ]);

        return redirect()->route('author.index')->with('success', 'تم إضافة المؤلف بنجاح');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Author $author
     * @return \Illuminate\Http\Response
     */
    public function show(Author $author)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Author $author
     * @return \Illuminate\Http\Response
     */
    public function edit(Author $author)
    {
        return view('panel.author.edit', compact('author'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Author $author
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Author $author)
    {
        $request->validate([
            'name' => 'required|string|max:191',
            'description' => 'nullable|string',
            'is_visible' => 'required|boolean',
        ]);

        $author->update([
            'name' => $request->post('name'),
            'description' => $request->post('description'),
            'is_visible' => $request->post('is_visible'),
        ]);

        return redirect()->back()->with('success', 'تم تحديث المؤلف بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Author $author
     * @return \Illuminate\Http\Response
     */
    public function destroy(Author $author)
    {
        if ($author->books()->count() > 0) {
            return redirect()->back()->with('warning', 'غير قادر على حذف المؤلف، تأكد من نقل الكتب الخاصة به');
        }
        // Delete record
        $author->delete();

        return redirect()->back()->with('success', 'تم المسح بنجاح!');
    }
}
