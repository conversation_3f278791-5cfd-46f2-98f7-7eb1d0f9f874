<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\PodcastResource;
use App\Http\Resources\ProgramResource;
use App\Program;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class ProgramApiController extends Controller
{

    /**
     *  Latest list of Programs
     * @return ProgramResource
     */
    public function index(Request $request)
    {
        return ProgramResource::collection(Program::latest()->get());
    }

    /**
     *  Latest trend of Programs
     * @return ProgramResource
     */
    public function trend(Request $request)
    {
        $programs = Program::withCount(['recentPlay'])
            ->orderBy('recent_play_count', 'desc')
            ->where('is_visible', true)
            ->paginate(10);

        return ProgramResource::collection($programs);
    }

    /**
     *  Latest list of Podcasts of specific Program
     * @param Program
     * @return PodcastResource
     */
    public function podcasts(Request $request, Program $program)
    {
        $podcasts = $program->podcasts()
            ->where('is_published', true)
            ->whereHas('program', function ($query) { return $query->where('is_visible', true); })
            ->paginate(10);

        return PodcastResource::collection($podcasts);
    }
}
