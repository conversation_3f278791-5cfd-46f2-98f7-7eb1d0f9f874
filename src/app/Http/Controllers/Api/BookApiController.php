<?php

namespace App\Http\Controllers\Api;

use App\Book;
use App\Category;
use App\Http\Controllers\Controller;
use App\Http\Resources\BookResource;
use App\Podcast;
use App\Services\PreferenceService;
use App\Services\UserLogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BookApiController extends Controller
{

    /**
     *  All books
     */
    public function all(Request $request)
    {
        $request->validate([
            'q' => 'nullable|string',
            'language' => 'nullable|string',
            'country' => 'nullable|string',
            'order_by' => 'nullable|string|in:id,title_ar,title_en,views,created_at,updated_at',
            'ordering' => 'nullable|string|in:ASC,DESC',
            'per_page' => 'nullable|integer',
            'category' => 'nullable|integer',
        ]);

        $user = Auth::guard('api')->user();

        $search = $request->q;
        $language = $request->language;
        $country = $request->country;
        $orderBy = $request->order_by ?? 'id';
        $ordering = $request->ordering == 'DESC' ? 'DESC' : 'ASC';
        $per_page = $request->per_page ?? 10;
        $category = $request->category;

        $books = Book::with(['authors', 'episodes'])
            ->where(function ($query) use ($search) {
                $query->where('title_ar', 'LIKE', '%' . $search . '%')
                    ->orWhere('title_en', 'LIKE', '%' . $search . '%')
                    ->orWhere('description_ar', 'LIKE', '%' . $search . '%')
                    ->orWhere('description_en', 'LIKE', '%' . $search . '%');
            })
            ->when($language, function ($query) use ($language) {
                $query->where('language', $language);
            })
            ->when($country, function ($query) use ($country) {
                $query->where('country', $country);
            })
            ->when($category, function ($query) use ($category) {
                $query->where('category_id', $category);
            })
            ->where('is_published', true)
            ->whereHas('authors', function ($query) { return $query->where('is_visible', true); })
            ->orderBy($orderBy, $ordering)
            ->paginate($per_page);

        if ($search and $user) {
            UserLogService::logSearchQuery($user, $search, 'book');
        }

        return BookResource::collection($books);
    }

    public function show(Book $book)
    {
        $book->increment('views');

        return BookResource::make($book);
    }

    /**
     *  Recommended books
     */
    public function recommended(Request $request)
    {
        $per_page = is_numeric($request->per_page)? (int) $request->per_page: 10;
        $user = Auth::guard('api')->user();

        $recommendedQuery = Book::with(['authors', 'episodes'])
            ->whereHas('authors', function ($query) { return $query->where('is_visible', true); })
            ->where('is_published', true);

        if($user) {
            $preferredBooks = $user->preferredContent()->where('preferable_content_type', Book::class)->pluck('preferable_content_id');
            $preferredCategories = $user->preferredContent()->where('preferable_content_type', Category::class)->pluck('preferable_content_id');

            $recommendedQuery = $recommendedQuery->where(function ($query) use ($preferredBooks, $preferredCategories) {
                return $query->whereIn('id', $preferredBooks)
                        ->orWhereIn('category_id', $preferredCategories);
            });
        }

        $recommended = $recommendedQuery
            // ->inRandomOrder()
            ->latest()
            ->paginate($per_page);

        if(count($recommended->items()) < $per_page and $recommended->currentPage() == 1) {
            $recommended = Book::where('is_published', true)
                ->whereHas('authors', function ($query) { return $query->where('is_visible', true); })
                // ->inRandomOrder()
                ->oldest()
                ->paginate($per_page);
        }

        return BookResource::collection($recommended);
    }

    /**
     *  Most read books
     */
    public function mostRead(Request $request)
    {
        $mostRead = Book::with(['authors', 'episodes'])->where('is_published', true)
            ->whereHas('authors', function ($query) { return $query->where('is_visible', true); })
            ->orderBy('views', 'DESC')
            ->paginate(10);

        return BookResource::collection($mostRead);
    }

    /**
     *  Last added books
     */
    public function newest(Request $request)
    {
        $newest = Book::with(['authors', 'episodes'])
            ->latest()
            ->where('is_published', true)
            ->whereHas('authors', function ($query) { return $query->where('is_visible', true); })
            ->paginate(10);

        return BookResource::collection($newest);
    }

    /**
     * Log to Recentrly Played
     */
    public function logToRecentlyPlayed(Request $request, Podcast $podcast)
    {
        $validator = validator($request->all(), [
            'seconds' => "required|integer|min:0",
            'progress' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response(['message' => 'Invalid request parameters!', 'errors' => $validator->errors()], 400);
        }

        $user = Auth::guard('api')->user();
        $progress = $request->post('progress');
        $seconds = $request->post('seconds');

        $podcast->recentPlay()->create([
            'user_id' => $user->id ?? null,
            'seconds' => $seconds,
            'progress' => $progress,
        ]);

        return response()->json(['success' => true, 'message' => 'Logged Successfully!']);
    }

    /**
     *  Like/Undo-Unlike particular Book
     */
    public function toggleLike(Request $request, Book $book)
    {
        $user = Auth::guard('api')->user();

        $like = $book->likes()->where('user_id', $user->id)->first();

        if (! is_null($like)) {
            $like->delete();

            PreferenceService::removeBookFromPreference($book, $user);
        } else {
            $like = $book->likes()->create(['user_id' => $user->id]);

            // Add user content preferences for Book, and category
            PreferenceService::addBookToPreference($book, $user);
        }

        return $this->show($book);
    }

    /**
     *  get like count of Book
     */
    public function likesCount(Request $request, Book $book)
    {
        $user = Auth::guard('api')->user();

        $likes = $book->likes()->count();

        return response()->json([
            'likes_count' => $likes,
        ]);
    }

    /**
     *  get user stats isLiked and isFavorited
     */
    public function userStats(Request $request, Book $book)
    {
        $user = Auth::guard('api')->user();

        return response()->json([
            'is_liked' => $book->likes()->where('user_id', $user->id)->exists(),
            // 'is_favorited' => $book->favorites()->where('user_id', $user->id)->exists(),
        ]);
    }
}
