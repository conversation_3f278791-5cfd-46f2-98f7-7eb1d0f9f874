<?php

namespace App\Http\Controllers\Api;

use App\Book;
use App\Http\Controllers\Controller;
use App\Http\Resources\BookResource;
use App\Http\Resources\SearchSuggestionResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SearchApiController extends Controller
{
    public function suggest(Request $request)
    {
        $search = $request->q;
        $perPage = $request->per_page ?? 10;

        if (strlen($search) < 2) {
            return SearchSuggestionResource::collection([]);
        }

        $games = Book::where('is_published', true)
            ->where(function ($query) use ($search) {
                $query->where('title_ar', 'like', '%' . $search . '%')
                    ->orWhere('title_en', 'like', '%' . $search . '%')
                    ->orWhere('description_ar', 'like', '%' . $search . '%')
                    ->orWhere('description_en', 'like', '%' . $search . '%');
            })
            ->orderBy('title_ar', 'asc')
            ->paginate($perPage);

        return SearchSuggestionResource::collection($games);
    }

    public function searchLogs(Request $request) {
        $user = Auth::guard('api')->user();

        if (! $user) {
            return SearchSuggestionResource::collection([]);
        }

        $perPage = $request->per_page ?? 10;

        $searchLogs = $user->searchLogs()
            ->select('query', DB::raw('max(created_at) as created_at'))
            ->groupBy('query')
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return SearchSuggestionResource::collection($searchLogs);
    }
}
