<?php

namespace App\Http\Controllers\Api;

use App\CachedVariable;
use App\Http\Resources\UserContentPreferenceResource;
use App\UserContentPreference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SettingsApiController extends ApiController
{
    /**
     *  Update user content preferences
     */
    public function deletePreference(Request $request, UserContentPreference $preference)
    {
        $user = Auth::guard('api')->user();

        if ($user->id != $preference->user_id) {
            return $this->basicResponse('أنت لا تملك صلاحية للتعديل على هذا المحتوى', false, 403);
        }

        $preference->delete();

        return $this->basicResponse('Deleted Successfully!');
    }

    /**
     *  Get user preferences
     */
    public function myPreferences(Request $request)
    {
        $user = Auth::guard('api')->user();

        $preferences = $user->preferredContent()->with('preferableContent')->get();

        return UserContentPreferenceResource::collection($preferences);
    }
}
