<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Auth\JWTController;
use App\Services\DemoService;
use App\Services\PaymentGateway;
use App\Services\SubscriptionService;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Tymon\JWTAuth\Facades\JWTAuth;
use Illuminate\Support\Str;

class PaymentGatwayController extends ApiController
{
    /**
     * Verify PIN and Subscribe user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $request->validate([
            'msisdn' => 'required|string|min:12|max:12|starts_with:24910,24911,24912,24991,24996,24990,24992,24999,2494',
        ]);

        $msisdn = $request->msisdn;

        if (in_array($msisdn, DemoService::allowedUsersPhones())) { // Login Demo users
            $user = SubscriptionService::getUser($msisdn);
            if ($token = JWTAuth::fromUser($user)) {
                return (new JWTController())->respondWithToken($token);
            }
        }

        $paymentGatway = new PaymentGateway();
        $subsResponse = $paymentGatway->checkSubscription($msisdn);
        $endSubDate = $subsResponse['endSubDate'];
        SubscriptionService::submitBilling($msisdn, $endSubDate, now());

        if ($subsResponse['is_subscribed']) {
            $user = SubscriptionService::getUser($msisdn);

            if ($token = JWTAuth::fromUser($user)) {
                return (new JWTController())->respondWithToken($token);
            }
        }

        return $this->basicResponse('عفواّ انت غير مشترك في هذه الخدمة', false, 401);
    }

    /**
     * Check user subscription.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkSubscription(Request $request)
    {
        $msisdn = auth('api')->user()->phone;

        if (in_array($msisdn, DemoService::allowedUsersPhones())) {
            return response()->json(DemoService::userSubStatus());
        }

        $paymentGatway = new PaymentGateway();
        $response = $paymentGatway->checkSubscription($msisdn);

        return response()->json($response);
    }

    /**
     * Check user subscription.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function unsubscribe(Request $request)
    {
        $msisdn = auth('api')->user()->phone;

        $paymentGatway = new PaymentGateway();
        $status = $paymentGatway->unsubscribe($msisdn);

        if ($status) {
            SubscriptionService::unsubscribe($msisdn);
            return $this->basicResponse('Successfully unsubscribed');
        }

        return $this->basicResponse('Unsubscription failed', false, 401);
    }

    /**
     * Push notification receiver for subscription status, from SPay
     */
    public function pushNotificationSubsStatus(Request $request)
    {
        if($request->method() == 'GET') { // AD Tracker notification

            Log::debug('Push Notification (AD Tracker)');
            Log::debug($request->all());

            $clickID = $request->click_id;

            if ($clickID) {
                $client = new Client();

                try {
                    Log::debug('Notify Ad Tracker');

                    $response = $client->post(env('POSTBACK_TRACKER_URL'), [
                        'json' => [
                            'payload' => [
                                'click_id' => $clickID,
                                'booktown_ad_forwarded_by_zain' => true,
                                'token' => env('POSTBACK_GG_AGENCY_TOKEN'),
                            ],
                            'service' => env('POSTBACK_TRACKER_SERVICE_ID'),
                        ],
                        'headers' => [
                            'Accept' => 'application/json',
                            'Content-Type' => 'application/json',
                            'Origin' => env('POSTBACK_TRACKER_ORIGIN')
                        ],
                    ]);

                    Log::debug($response->getBody());

                } catch (Exception $ex) {
                    Log::error('Exception @Push Notification (AD Tracker): ' . $ex->getMessage());
                }
            }

        } else { // Push Notification of subscription status update

            $msisdn = $request->msisdn;
            $status = $request->status;
            $expire_date = $request->unsub_date;
            // $sub_date = $request->sub_date;
            // $product_code = $request->product_code;
            // $price = $request->price;

            Log::debug('Push Notification');
            Log::debug($request->all());

            if ($status) {
                SubscriptionService::submitBilling($msisdn, $expire_date);
                SubscriptionService::submitTransaction($msisdn, $expire_date);
            }
        }

        return response()->json([
            "success" => true,
        ]);
    }

    /**
     * Push notification receiver for Header Enrichment, from SPay
     */
    public function redirectHE(Request $request, $msisdn)
    {
        $paymentGatway = new PaymentGateway();
        $subs = $paymentGatway->checkSubscription($msisdn);
        $endSubDate = $subs['endSubDate'];
        $creationDate = $subs['creationDate'];
        $is_subscribed = $subs['is_subscribed'];

        Log::debug('Header Enreachment');
        Log::debug("Phone: ${msisdn}");
        Log::debug('Subs: ');
        Log::debug($subs);

        SubscriptionService::submitBilling($msisdn, $endSubDate, $creationDate);

        if($is_subscribed) {
            SubscriptionService::submitTransaction($msisdn, $endSubDate);

            $user = SubscriptionService::getUser($msisdn);
            if ($token = JWTAuth::fromUser($user)) {
                $uuid = Str::uuid();
                $sha1 = sha1($uuid . now());
                return redirect(env('SPA_PORTAL_URL') . "?ref=DSP&s=${sha1}=1&p=${msisdn}&t=${token}&tt=${uuid}");
            }
        }

        return redirect(env('ZAIN_PAYMENT_LANDING'));
    }
}
