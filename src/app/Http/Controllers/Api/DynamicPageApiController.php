<?php

namespace App\Http\Controllers\Api;

use App\DynamicPage;
use App\Http\Controllers\Controller;
use App\Http\Resources\DynamicPageResource;
use Illuminate\Http\Request;

class DynamicPageApiController extends Controller
{
    /**
     *  Show specific Page
     */
    public function show($uuid)
    {
        $dynamicPage = DynamicPage::where('uuid', $uuid)->firstOrFail();

        return DynamicPageResource::make($dynamicPage);
    }

}
