<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\SlideResource;
use App\Slide;
use Illuminate\Http\Request;

class SlideApiController extends Controller
{
    /**
     *  Main slide queue
     */
    public function home(Request $request)
    {
        $slides = Slide::with(['slidable'])->where('is_visible', true)->orderBY('sequence')->get();
        return SlideResource::collection($slides);
    }
}