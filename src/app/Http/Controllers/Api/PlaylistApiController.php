<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\PlaylistResource;
use App\Http\Resources\PodcastResource;
use App\Playlist;
use App\Podcast;
use Illuminate\Http\Request;

class PlaylistApiController extends Controller
{

    /**
     *  Get podcasts of the playlist
     */
    public function podcasts(Request $request, Playlist $playlist)
    {
        return PodcastResource::collection($playlist->podcasts);
    }

    /**
     *  Playlist of Authenticated User
     */
    public function me(Request $request)
    {
        # TODO
    }

    /**
     *  Popular Playlist Added by Admins
     */
    public function popular(Request $request)
    {
        # TODO
    }

    /**
     *  Create User Playlist
     */
    public function create(Request $request, Playlist $playlist)
    {
        # TODO
    }

    /**
     *  Add Podccast to Playlist
     */
    public function addToPlaylist(Request $request, Podcast $podcast, Playlist $playlist)
    {
        # TODO
    }

    /**
     *  Add Podccast to Playlist
     */
    public function reomveFromPlaylist(Request $request, Podcast $podcast, Playlist $playlist)
    {
        # TODO
    }

    /**
     *  Delete User Playlist
     */
    public function destroy(Request $request, Playlist $playlist)
    {
        // Casecade and delete all added podcasts' pivotes
        $playlist->detach();

        // Delete playlist
        $playlist->delete();

        return response(['status' => 'success']);
    }
}