<?php

namespace App\Http\Controllers\Api;

use App\Category;
use App\Http\Controllers\Controller;
use App\Http\Resources\BookResource;
use App\Http\Resources\PodcastResource;
use App\Http\Resources\ProgramResource;
use Illuminate\Http\Request;

class CategoryApiController extends Controller
{
    /**
     *  Latest Categories
     */
    public function index()
    {
        $categories = Category::where('is_visible', true)->latest()->get();
        return response($categories);
    }

    /**
     *  Show specific Category with its Programs
     */
    public function show(Category $category)
    {
        return response($category);
    }

    /**
     *  Show specific Category with its Programs
     */
    public function programs(Category $category)
    {
        return ProgramResource::collection($category->programs()->where('programs.is_visible', true)->paginate(10));
    }

    /**
     *  Latest list of Books of specific Category
     * @param Program
     * @return PodcastResource
     */
    public function books(Request $request, Category $category)
    {
        $books = $category->books()
            ->where('is_published', true)
            ->whereHas('authors', function ($query) { return $query->where('is_visible', true); })
            ->paginate(10);

        return BookResource::collection($books);
    }
}
