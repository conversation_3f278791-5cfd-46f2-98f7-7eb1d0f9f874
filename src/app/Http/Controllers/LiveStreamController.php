<?php

namespace App\Http\Controllers;

use App\LiveStream;
use App\Program;
use Illuminate\Http\Request;

class LiveStreamController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('panel.stream.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'url' => 'required|url',
            'program' => 'required|integer',
        ]);

        $liveStream = LiveStream::create([
            'url' => $request->post('url'),
            'program_id' => Program::findOrFail($request->post('program'))->id, // Check before insert
        ]);

        return redirect()->route('program.show', $liveStream->program_id)->with('success', 'تمت إضافة رابط البث بنجاح');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\LiveStream  $liveStream
     * @return \Illuminate\Http\Response
     */
    public function show(LiveStream $liveStream)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\LiveStream  $liveStream
     * @return \Illuminate\Http\Response
     */
    public function edit(LiveStream $liveStream)
    {
        return view('panel.stream.create', compact('liveStream'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\LiveStream  $liveStream
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, LiveStream $liveStream)
    {
        $request->validate([
            'url' => 'required|url',
            'program' => 'required|integer',
        ]);

        $liveStream->url = $request->post('url');
        $liveStream->program_id = Program::findOrFail($request->post('program'))->id; // Check before insert
        $liveStream->save();

        return redirect()->back()->with('success', 'تم تحديث الرابط بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\LiveStream  $liveStream
     * @return \Illuminate\Http\Response
     */
    public function destroy(LiveStream $liveStream)
    {
        $liveStream->delete();

        return redirect()->back()->with('success', 'تم حذف الرابط بنجاح');
    }
}