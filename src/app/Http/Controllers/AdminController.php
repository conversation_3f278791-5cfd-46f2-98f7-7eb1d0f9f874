<?php

namespace App\Http\Controllers;

use App\User;
use Illuminate\Http\Request;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('authorize:manage_admins');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $admins = User::where('type', 'Admin')->latest()->with('roles')->paginate(10);

        return view('panel.admin.index', ['admins' => $admins]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('panel.admin.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Validate inputs
        $request->validate([
            'name' => 'required|string|max:191',
            'email' => 'required|email|max:191|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'roles' => 'required|array',
        ]);

        // Create an admin
        $admin = User::create([
            'type' => 'Admin',
            'name' => $request->input('name'),
            'email' => $request->input('email'),
            'phone' => rand(1000000000, 9999999999),
            'password' => bcrypt($request->input('password')),
        ]);

        // attach roles
        $admin->roles()->attach($request->input('roles'));

        return redirect()->route('admin.index')->with('success', 'تم إضافة المشرف بنجاح!');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $admin = User::whereType('Admin')->whereId($id)->firstOrFail();

        return view('panel.admin.edit', ['admin' => $admin]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

        // Get Admin Model
        $admin = User::whereType('Admin')->whereId($id)->firstOrFail();

        $request->validate([
            'name' => 'required|string|max:191',
        ]);

        // check if admin want to change user password
        if (!empty($request->input('password'))) {
            $request->validate([
                'password' => 'required|min:8|confirmed'
            ]);

            $admin->update([
                'password' => bcrypt($request->input('password'))
            ]);
        }

        // Update rest of info
        $admin->update([
            'name' => $request->input('name'),
        ]);

        // Update User Roles
        $role_ids = $request->input('roles');
        $admin->roles()->sync($role_ids);

        return back()->with('success', 'تم تحديث بيانات المشرف بنجاح');
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // Get Admin Model
        $admin = User::whereType('Admin')->whereId($id)->firstOrFail();
        $admin->roles()->sync([]);
        $admin->delete();

        return redirect()->back()->with('success', 'تم حذف المشرف بشكل نهائي.');
    }
}