<?php

namespace App\Http\Controllers;

use App\Author;
use App\Book;
use App\Category;
use App\Slide;
use App\Subscription;
use App\User;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $statistics = [
            'books_count' => Book::all()->count(),
            'categories_count' => Category::all()->count(),
            'authors_count' => Author::all()->count(),
            'active_slides_count' => Slide::where('is_visible', true)->count(),
            'users_count' => User::where('type', 'Subscriber')->count(),
            'subscribers_count' => Subscription::where('is_subscribed', true)->count(),
            'new_subscribers_count' => Subscription::where('is_subscribed', true)->whereDate('created_at', '>', now()->subDays(7))->count(),
            'active_subscribers_count' => Subscription::where('is_subscribed', true)->whereDate('active_billing_until', '>', now()->subHours(Subscription::FREE_PREMIUM_HOURS_OFFSET))->count(),
        ];

        return view('home', $statistics);
    }
}
