<?php

namespace App\Http\Controllers;

use App\Poet;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class PoetController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $poets = Poet::latest()->paginate(10);

        return view('panel.poet.index', compact('poets'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('panel.poet.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'description' => 'required|string',
            'photo' => 'required|image|mimes:jpg,png,jpeg',
        ]);

        $user = User::create([
            'name' => $request->post('name'),
            'email' => Str::uuid() . '@podcast.com',
            'password' => bcrypt('123456-podcast'),
            'type' => 'poet',
        ]);

        $poet = $user->poet()->create([
            'name' => $request->post('name'),
            'description' => $request->post('description'),
            'photo' => $request->file('photo')->store('public/poets/' . date('Y/m')),
        ]);

        return redirect()->route('poet.index')->with('success', 'تمت اضافة الشاعر بنجاح!');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Poet  $poet
     * @return \Illuminate\Http\Response
     */
    public function show(Poet $poet)
    {
        return view('panel.poet.show', compact('poet'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Poet  $poet
     * @return \Illuminate\Http\Response
     */
    public function edit(Poet $poet)
    {
        return view('panel.poet.edit', compact('poet'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Poet  $poet
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Poet $poet)
    {
        $request->validate([
            'name' => 'required|string',
            'description' => 'required|string',
        ]);

        if ($request->hasFile('photo')) {
            $request->validate([
                'photo' => 'required|image|mimes:jpg,png,jpeg',
            ]);

            // Delete old photo
            Storage::delete($poet->photo);

            $poet->photo = $request->file('photo')->store('public/poets/' . date('Y/m'));
        }

        $poet->name = $request->post('name');
        $poet->description = $request->post('description');
        $poet->save();

        return redirect()->back()->with('success', 'تم التعديل بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Poet  $poet
     * @return \Illuminate\Http\Response
     */
    public function destroy(Poet $poet)
    {
        // Delete files first
        Storage::delete($poet->photo);

        // Delete App\User record
        $poet->user()->delete();

        // Delete record
        $poet->delete();

        return redirect()->back()->with('success', 'تم المسح بنجاح!');
    }
}
