<?php

namespace App\Http\Controllers;

use App\Book;
use App\Slide;
use Illuminate\Http\Request;

class SlideController extends Controller
{

    public function __construct()
    {
        $this->middleware('authorize:manage_slides');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $slides = Slide::with('slidable')->orderBy('sequence')->get();

        $books = Book::latest()->get();

        return view('panel.slide.index', compact('slides', 'books'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'slidable_id' => 'required|integer',
            'slidable_type' => 'required|string',
        ]);

        $exists = Slide::where('slidable_id', $request->slidable_id)->where('slidable_type', $request->slidable_type)->exists();
        if($exists) {
            return redirect()->back()->with('warning', 'هذا الكتاب موجود في عرض الشرائح مسبقا');
        }

        // Insert Slide record
        Slide::create([
            'slidable_id' => $request->slidable_id,
            'slidable_type' => $request->slidable_type,
            'sequence' => Slide::getUniqueSequence(),
        ]);

        return redirect()->route('slide.index')->with('success', 'تمت اﻹضافة بنجاح!');

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Slide  $slide
     * @return \Illuminate\Http\Response
     */
    public function show(Slide $slide)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Slide  $slide
     * @return \Illuminate\Http\Response
     */
    public function edit(Slide $slide)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Slide  $slide
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Slide $slide)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Slide  $slide
     * @return \Illuminate\Http\Response
     */
    public function destroy(Slide $slide)
    {
        //
    }

    /**
     *  Toggle slide visibility
     * @param Slide
     * @return Slide
     */
    public function toggleVisibilityJson(Request $request, Slide $slide)
    {
        $slide->is_visible = !$slide->is_visible;
        $slide->save();
        return response($slide);
    }

    /**
     * Swap two slides sequence
     *
     * @param Slide $slide1
     * @param Slide $slide2
     * @return Slide $slide1
     */
    public function swapSequenceJson(Request $request, Slide $slide1, Slide $slide2)
    {
        $tempSequence = $slide1->sequence;
        $slide1->sequence = $slide2->sequence;
        $slide2->sequence = $tempSequence;

        $slide1->save();
        $slide2->save();

        return response($slide1);
    }
}
