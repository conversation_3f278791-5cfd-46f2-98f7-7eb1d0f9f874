<?php

namespace App\Http\Controllers;

use App\Category;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function __construct()
    {
        $this->middleware('authorize:manage_categories');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $categories = Category::latest()->paginate(10);

        return view('panel.category.index', compact('categories'));
    }

    /**
     *  Latest Categories json response
     */
    public function indexJson()
    {
        $categories = Category::latest()->get();
        return response($categories);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('panel.category.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name_ar' => 'required|string|max:191',
            'name_en' => 'required|string|max:191',
            'is_visible' => 'required|boolean',
        ]);

        Category::create([
            'name_ar' => $request->post('name_ar'),
            'name_en' => $request->post('name_en'),
            'is_visible' => $request->post('is_visible'),
        ]);

        return redirect()->route('category.index')->with('success', 'تم إضافة التصنيف بنجاح');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Category  $category
     * @return \Illuminate\Http\Response
     */
    public function show(Category $category)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Category  $category
     * @return \Illuminate\Http\Response
     */
    public function edit(Category $category)
    {
        return view('panel.category.edit', compact('category'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Category  $category
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Category $category)
    {
        $request->validate([
            'name_ar' => 'required|string|max:191',
            'name_en' => 'required|string|max:191',
            'is_visible' => 'required|boolean',
        ]);

        $category->update([
            'name_ar' => $request->post('name_ar'),
            'name_en' => $request->post('name_en'),
            'is_visible' => $request->post('is_visible'),
        ]);

        return redirect()->back()->with('success', 'تم تحديث التصنيف بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Category  $category
     * @return \Illuminate\Http\Response
     */
    public function destroy(Category $category)
    {
        if ($category->programs()->count() > 0) {
            return redirect()->back()->with('warning', 'غير قادر على حذف التصنيف، تأكد من نقل البرامج الخاصة به');
        }
        // Delete record
        $category->delete();

        return redirect()->back()->with('success', 'تم المسح بنجاح!');
    }
}
