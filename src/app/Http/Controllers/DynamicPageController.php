<?php

namespace App\Http\Controllers;

use App\DynamicPage;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class DynamicPageController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $dynamicPages = DynamicPage::latest()->paginate(10);

        return view('panel.daynamic_page.index', compact('dynamicPages'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('panel.daynamic_page.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'title_ar' => 'required|string|max:191',
            'title_en' => 'required|string|max:191',
            'content_ar' => 'required|string',
            'content_en' => 'required|string',
        ]);

        DynamicPage::create([
            'title_ar' => $request->title_ar,
            'title_en' => $request->title_en,
            'content_ar' => $request->content_ar,
            'content_en' => $request->content_en,
            'uuid' => Str::uuid(),
        ]);

        return redirect()->route('dynamicPage.index')->with('success', 'تم إضافة الصفحة بنجاح.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\DynamicPage  $dynamicPage
     * @return \Illuminate\Http\Response
     */
    public function show(DynamicPage $dynamicPage)
    {
        return view('panel.daynamic_page.show', compact('dynamicPage'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\DynamicPage  $dynamicPage
     * @return \Illuminate\Http\Response
     */
    public function edit(DynamicPage $dynamicPage)
    {
        return view('panel.daynamic_page.edit', compact('dynamicPage'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\DynamicPage  $dynamicPage
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, DynamicPage $dynamicPage)
    {
        $request->validate([
            'title_ar' => 'required|string|max:191',
            'title_en' => 'required|string|max:191',
            'content_ar' => 'required|string',
            'content_en' => 'required|string',
        ]);

        $dynamicPage->update([
            'title_ar' => $request->title_ar,
            'title_en' => $request->title_en,
            'content_ar' => $request->content_ar,
            'content_en' => $request->content_en,
        ]);

        return redirect()->route('dynamicPage.index')->with('success', 'تم إضافة الصفحة بنجاح.');
    }

    /**
     * Toggle page visibility.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\DynamicPage  $dynamicPage
     * @return \Illuminate\Http\Response
     */
    public function toggleVisibility(Request $request, DynamicPage $dynamicPage)
    {
        $dynamicPage->is_visible = !$dynamicPage->is_visible;
        $dynamicPage->save();
        return back()->with('success', 'تم تغيير الظهور بنجاح.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\DynamicPage  $dynamicPage
     * @return \Illuminate\Http\Response
     */
    public function destroy(DynamicPage $dynamicPage)
    {
        $dynamicPage->delete();

        return redirect()->route('dynamicPage.index')->with('success', 'تم حذف الصفحة بنجاح.');
    }
}
