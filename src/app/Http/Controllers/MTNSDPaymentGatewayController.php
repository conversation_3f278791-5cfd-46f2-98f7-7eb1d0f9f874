<?php

namespace App\Http\Controllers;

use App\Helpers\Helper;
use App\Services\SubscriptionService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use Illuminate\Support\Str;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

class MTNSDPaymentGatewayController extends Controller
{

    /**
     * Push notification from (MTN)
     */
    public function redirect(Request $request)
    {
        $request->merge(['operator' => 'mtn']);
        Log::debug('(MTN) Redirect');
        Log::debug($request->all());

        $msisdn = $request['CGMSISDN'];
        $status = $request['CGSTATUS'];

        $expire_date = now()->addDay();
        SubscriptionService::submitBilling($msisdn, $expire_date);
        SubscriptionService::submitTransaction($msisdn, $expire_date, 0);

        $user = SubscriptionService::getUser($msisdn);
        $token = $user->createToken("API Token")->plainTextToken;
        $uuid = Str::uuid();
        $sha1 = sha1($uuid . now());
        return redirect(env('MTN_SPA_PORTAL_URL', 'https://zaytoon.digitalplusteq.com') . "?ref=MTN&s={$sha1}=1&p={$msisdn}&u={$token}&tt={$uuid}");
    }

    public function callback(Request $request)
    {
        $request->merge(['operator' => 'mtn']);
        try {
            $msisdn = $request['MSISDN'] ?? '';
            $status = $request['STATUS'];
            $price = $request['Price'] ?? 0;
            $date = $request['Date'] ?? null;
            $click_id = $request['click_id'];

            Log::debug('(MTN) Push Notification');
            Log::debug($request->all());
            // Log::debug("Phone: ${msisdn}");

            switch ($status) {
                case 'ACT-SB': // “Subscriber” End-User Subscribe in the service
                case 'FSC-BL': // Success to bill the subscriber for the first time, End-User Become Active Subscriber.
                case 'RSC-BL': // Success renewal billing for the subscriber
                    $expire_date = $date ? Carbon::parse($date)->addDay() : now()->addDay();
                    SubscriptionService::submitBilling($msisdn, $expire_date);
                    SubscriptionService::submitTransaction($msisdn, $expire_date, $price);
                    break;

                case 'BLD-SB': // “Blocked Subscriber” End-User un-Subscribe from the service
                case 'RCL-SB': // Recycled from operator side
                    SubscriptionService::unsubscribe($msisdn);
                    break;

                case 'RFL-BL': // Failed renewal billing for the subscriber
                    SubscriptionService::submitBilling($msisdn, null);
                    break;

                case 'FFL-BL': // Failed to bill the subscriber for the first time
                    // do nothing
                    break;
            }

            // Notify ad tracker if click_id is present and status is 'ACT-SB' (New subscriber)
            if ($status == 'ACT-SB') {
                $this->notifyAdTracker($click_id);
            }

        } catch (Exception $ex) {
            Log::debug("MTN: Push Notification: " . $ex->getMessage());
        }

        return response()->json([
            'RequestID' => $request['RequestId'],
            'MapID' => date('Ymdhis') . rand(10000, 99999),
        ]);
    }

    /**
     * Check user subscription.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkSubscription(Request $request)
    {
        $user = auth('api')->user();

        // if (in_array($msisdn, DemoService::allowedUsersPhones())) {
        //     return response()->json(DemoService::userSubStatus());
        // }

        $response = [
            'is_subscribed' => $user->is_subscribed,
            'has_active_billing' => $user->has_active_billing,
            'subDate' => $user->subscription->created_at,
            'endSubDate' => $user->subscription->active_billing_until,
            'creationDate' => $user->subscription->created_at,
        ];

        return response()->json($response);
    }

    /**
     * Notify ad tracker.
     *
     * @param $click_id
     * @return \Illuminate\Http\JsonResponse
     */
    private function notifyAdTracker($click_id)
    {
        if ($click_id) {
            $client = new Client();

            try {
                Log::debug('[MTN] Notify Ad Tracker');

                $ggPayLoad = [
                    'click_id' => $click_id,
                    'qawafi_ad_forwarded_by_mtn_from_gg_agency' => true,
                    'token' => env('POSTBACK_GG_AGENCY_TOKEN'),
                ];

                $level23Payload = [
                    'currency' => 'USD',
                    'handler' => env('POSTBACK_LEVEL23_HANDLER', 11493),
                    'hash' => env('POSTBACK_LEVEL23_HASH', '7ec0d431c8b1aa80f4e5baf4c9e62be9'),
                    'tracker' => $click_id,
                    'qawafi_ad_forwarded_by_mtn_from_traffic_company' => true,
                ];

                $mobipiumlinkPayload = [
                    'click_id' => $click_id,
                    'qawafi_ad_forwarded_by_mtn_from_mobipiumlink' => true,
                ];

                switch(Helper::identifyClickIdProvider($click_id)) {
                    case 'gg-agency':
                        $payload = $ggPayLoad;
                        break;
                    case 'traffic-company':
                        $payload = $level23Payload;
                        break;
                    case 'mobipium':
                    default:
                        $payload = $mobipiumlinkPayload;
                }

                $response = $client->post(env('POSTBACK_TRACKER_URL'), [
                    'json' => [
                        'payload' => $payload,
                        'service' => env('POSTBACK_TRACKER_MTN_SERVICE_ID'),
                    ],
                    'headers' => [
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                        'Origin' => env('POSTBACK_TRACKER_ORIGIN'),
                    ],
                ]);

                Log::debug($response->getBody());

            } catch (Exception $ex) {
                Log::error('Exception @Push Notification (AD Tracker): ' . $ex->getMessage());
            }
        }
    }

}
