<?php

namespace App\Http\Controllers;

use App\Book;
use App\Episode;
use App\File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class EpisodeController extends Controller
{
    /**
     * Show the form for creating a new resource.
     *
     * @param \App\Book
     * @return \Illuminate\Http\Response
     */
    public function create(Book $book)
    {
        return view('panel.episode.create', compact('book'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Book
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, Book $book)
    {
        $request->validate([
            'episode_number' => 'required|integer|min:1',
            'episode_title_ar' => 'required|string|max:191',
            'episode_title_en' => 'required|string|max:191',
            'episode_audio_file' => 'required|file',
        ]);

        DB::transaction(function () use ($request, $book) {

            $episode = Episode::create([
                'episode_number' => $request->episode_number,
                'title_ar' => $request->episode_title_ar,
                'title_en' => $request->episode_title_en,
            ]);

            $book->episodes()->save($episode);


            $audio_file = new File([
                'uuid' => Str::uuid(),
                'url' => $request->file('episode_audio_file')->store('public/books/audios/' . date('Y/m')),
                'size' => $request->file('episode_audio_file')->getSize(),
                'length' => '0',
            ]);

            $episode->file()->save($audio_file);

        });

        return redirect()->route('book.show', $book)->with('success', 'تم إضافة الحلقة بنجاح');
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Book
     * @param  \App\Episode  $episode
     * @return \Illuminate\Http\Response
     */
    public function show(Book $book,Episode $episode)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Book
     * @param  \App\Episode  $episode
     * @return \Illuminate\Http\Response
     */
    public function edit(Book $book, Episode $episode)
    {
        return view('panel.episode.edit', compact('book', 'episode'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param \App\Book
     * @param  \App\Episode  $episode
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Book $book, Episode $episode)
    {
        $request->validate([
            'episode_number' => 'required|integer|min:1',
            'episode_title_ar' => 'required|string|max:191',
            'episode_title_en' => 'required|string|max:191',
            'episode_audio_file' => 'nullable|file',
        ]);

        $episode->update([
            'episode_number' => $request->episode_number,
            'title_ar' => $request->episode_title_ar,
            'title_en' => $request->episode_title_en,
        ]);

        if($request->hasFile('episode_audio_file')) {
            Storage::delete($episode->file->url);
            $episode->file()->update([
                'url' => $request->file('episode_audio_file')->store('public/books/audios/' . date('Y/m')),
                'size' => $request->file('episode_audio_file')->getSize(),
                'length' => '0',
            ]);
        }

        return redirect()->route('book.show', $book)->with('success', 'تم تحدبث بيانات الحلقة بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Book
     * @param  \App\Episode  $episode
     * @return \Illuminate\Http\Response
     */
    public function destroy(Book $book, Episode $episode)
    {
        $episode->delete();
        Storage::delete($episode->file->url);
        $episode->file()->delete();

        return redirect()->route('book.show', $book)->with('success', 'تم حذف الحلقة بنجاح');
    }
}
