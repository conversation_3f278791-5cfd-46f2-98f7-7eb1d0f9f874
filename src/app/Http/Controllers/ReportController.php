<?php

namespace App\Http\Controllers;

use App\Like;
use App\Podcast;
use App\Program;
use App\RecentlyPlayed;
use App\Subscription;
use App\Vendor;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;

class ReportController extends Controller
{

    /**
     * Display a trending Podcasts.
     *
     * @return \Illuminate\Http\Response
     */
    public function trend(Request $request)
    {
        $fromDate = $request->has('fromDate') && !empty($request->get('fromDate')) ? $request->get('fromDate') : '2000-01-01T00:00:01.01Z';
        $toDate = $request->has('toDate')  && !empty($request->get('toDate')) ? $request->get('toDate') : now()->addDay(1);

        $program = $request->has('program')  && !empty($request->get('program')) ? $request->get('program') : '%%';
        $vendor = ($request->has('vendor')  && !empty($request->get('vendor')))
            && !($request->has('program')  && !empty($request->get('program'))) // if program is filtered, no need to filter vendors
            ? $request->get('vendor') : null;


        // return [$fromDate];
        $ordering = $request->get('ordering') == 'ASC' ? 'ASC' : 'DESC';
        $orderBy = $request->get('orderBy') == 'likes' ? 'likes' : 'seconds';

        $podcasts = Podcast::with(['program.vendor'])->addSelect([
            'seconds' => RecentlyPlayed::selectRaw('sum(seconds) as seconds')
                ->whereColumn('podcasts.id', 'podcast_id')
                ->where('created_at', '>=', $fromDate)
                ->where('created_at', '<=', $toDate)
                ->groupBy('podcast_id'),
            'likes' => Like::selectRaw('count(id) as likes')
                ->whereColumn('podcasts.id', 'likable_id')
                ->where('created_at', '>=', $fromDate)
                ->where('created_at', '<=', $toDate)
                ->groupBy('likable_id'),

        ])
            ->where('program_id', 'LIKE', $program)
            ->orderBy($orderBy, $ordering)
            ->get();

        if (!is_null($vendor)) {
            $data = [];
            foreach ($podcasts as $podcast) {
                if (!empty($vendor) && $podcast->program->vendor->id == $vendor) {
                    $data[] = $podcast;
                }
            }
            $podcasts = $data;
        }

        $podcasts = collect($podcasts);

        // return $podcasts;

        return view('panel.report.podcasts', compact('podcasts'));
    }

    public function subscriptions_summary(Request $request)
    {
        $subs_stats = Subscription::selectRaw("
            count(id) AS count,
            DATE_FORMAT(created_at, '%Y-%m-%d') AS subs_date
        ")
            ->when($request->fromDate, function ($q) use ($request) {
                $q->where('created_at', '>=', $request->fromDate);
            })
            ->when($request->toDate, function ($q) use ($request) {
                $q->where('created_at', '<=', $request->toDate . ' 23:59:59');
            })
            ->groupBy('subs_date')
            ->orderByDesc('subs_date')
            ->get();


        try {
            $lastestDayFilterDate = isset($request->toDate) && !empty($request->toDate) ? Carbon::parse($request->toDate)->endOfDay(): now()->endOfDay();
            $firstDayFilterDate = isset($request->fromDate) && !empty($request->fromDate) ? Carbon::parse($request->fromDate)->startOfDay(): now()->subMonth()->startOfDay();
        } catch (Exception $ex) {
            $lastestDayFilter = now()->endOfDay();
            $firstDayFilterDate = now()->subMonth()->startOfDay();
        }

        $stats_temp = [];
        $loopingDate = $lastestDayFilterDate;

        while ($loopingDate >= $firstDayFilterDate) {
            $existance_date = $subs_stats->filter(function ($stat) use ($loopingDate) {
                return $loopingDate == Carbon::parse($stat->subs_date)->endOfDay();
            });
            $is_exist = (bool) count($existance_date);
            if($is_exist) {
                $stats_temp[] = $existance_date->first()->toArray();
            } else {
                $stats_temp[] = [
                    'count' => 0,
                    'subs_date' => $loopingDate->format('Y-m-d')
                ];
            }
            $loopingDate = $loopingDate->subDay();
        }

        $subs_stats = $stats_temp;

        return view('panel.report.subscriptions_summary', compact('subs_stats'));
    }
}
