<?php

namespace App\Http\Controllers\Auth;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use App\User;
use Illuminate\Support\Facades\Validator;
use JWTAuth;
use App\Helpers\SMS;
use App\Http\Resources\DemoUserResource;
use App\Http\Resources\UserResource;
use App\Services\DemoService;

class JWTController extends Controller
{
    /**
     * Create a new AuthController instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:api', ['except' => ['login']]);
    }

    /**
     * Get a JWT token via given credentials.
     *
     * @param  \Illuminate\Http\Request  $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        // $validator = $this->validateData($request);

        // if ($validator->fails()) {
        //     return response(['erorr' => $validator->errors()]);
        // }

        if ($token = JWTAuth::fromUser($this->attempt($request))) {
            return $this->respondWithToken($token);
        }

        return response()->json(['error' => 'Unauthorized'], 401);
    }

    /**
     *  Login Attempt
     *
     * @param  \Illuminate\Http\Request  $request
     *
     * @return \App\User
     */
    public function attempt(Request $request)
    {
        $user = User::where($this->credentials($request))->first();

        return !is_null($user) ? $user : $this->register($request);
    }

    /**
     *  Fetch credentilas from request
     *
     * @param  \Illuminate\Http\Request  $request
     *
     * @return Array
     */
    public function credentials(Request $request)
    {
        return $request->only('phone');
    }

    /**
     *  Register new Subscriber
     */
    public function register(Request $request)
    {
        $user = User::create($this->data($request));

        return $user;
    }

    /**
     *  Request data
     */
    public function data(Request $request)
    {
        return array_merge($this->credentials($request), [
            'email' => 'default.' . date('ymdis') . '.' . rand(100, 1000) . "@zaytoonsd.com",
            'name' => 'default',
            'password' => bcrypt(rand(9999, 99999999)),
            'type' => 'Subscriber',
        ]);
    }

    /**
     *  Validate request data
     */
    public function validateData(Request $request)
    {
        // return Validator::make($this->data($request), [
        //     'phone' => 'required|string|unique:users',
        // ]);
    }

    /**
     * Get the authenticated User
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function me()
    {
        $user  = $this->guard()->user();

        if (in_array($user->phone, DemoService::allowedUsersPhones())) {
            return DemoUserResource::make($user);
        }

        return UserResource::make($user);
    }

    /**
     * Log the user out (Invalidate the token)
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout()
    {
        $this->guard()->logout();

        return response()->json(['message' => 'Successfully logged out']);
    }

    /**
     * Refresh a token.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function refresh()
    {
        return $this->respondWithToken($this->guard()->refresh());
    }

    /**
     * Get the token array structure.
     *
     * @param  string $token
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function respondWithToken($token)
    {
        return response()->json([
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => $this->guard()->factory()->getTTL() * 60
        ]);
    }

    /**
     * Get the guard to be used during authentication.
     *
     * @return \Illuminate\Contracts\Auth\Guard
     */
    public function guard()
    {
        return Auth::guard('api');
    }
}
