<?php

namespace App\Http\Controllers;

use App\Ringtone;
use Illuminate\Http\Request;

class RingtoneController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Ringtone  $ringtone
     * @return \Illuminate\Http\Response
     */
    public function show(Ringtone $ringtone)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Ringtone  $ringtone
     * @return \Illuminate\Http\Response
     */
    public function edit(Ringtone $ringtone)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Ringtone  $ringtone
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Ringtone $ringtone)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Ringtone  $ringtone
     * @return \Illuminate\Http\Response
     */
    public function destroy(Ringtone $ringtone)
    {
        //
    }
}
