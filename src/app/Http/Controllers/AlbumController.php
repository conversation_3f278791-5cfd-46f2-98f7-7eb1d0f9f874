<?php

namespace App\Http\Controllers;

use App\Album;
use App\Poet;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AlbumController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $albums = Album::latest()->with('poet')->paginate(10);

        return view('panel.album.index', compact('albums'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('panel.album.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'thumbnail' => 'required|image|mimes:jpg,jpeg,png',
            'name_ar' => 'required|string|max:191',
            'name_en' => 'required|string|max:191',
            'poet' => 'required|integer',
        ]);

        Album::create([
            'thumbnail' => $request->file('thumbnail')->store('public/albums/' . date('Y/m')),
            'name_ar' => $request->post('name_ar'),
            'name_en' => $request->post('name_en'),
            'poet_id' => $request->post('poet'),
            'description' => $request->post('description'),
        ]);

        return redirect()->route('album.index')->with('success', 'تمت الإضافة بنجاح');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Album  $album
     * @return \Illuminate\Http\Response
     */
    public function show(Album $album)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Album  $album
     * @return \Illuminate\Http\Response
     */
    public function edit(Album $album)
    {
        return view('panel.album.edit', compact('album'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Album  $album
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Album $album)
    {
        $request->validate([
            'name_ar' => 'required|string|max:191',
            'name_en' => 'required|string|max:191',
            'poet' => 'required|integer',
        ]);

        if ($request->hasFile('thumbnail')) {
            $request->validate([
                'thumbnail' => 'required|image|mimes:jpg,jpeg,png',
            ]);

            // Delete old one
            Storage::delete('thumbnail');

            // Upload new one
            $album->thumbnail = $request->file('thumbnail')->store('public/albums/' . date('Y/m'));
        }

        $album->name_ar = $request->post('name_ar');
        $album->name_en = $request->post('name_en');
        $album->poet_id = Poet::findOrFail($request->post('poet'))->id; // Double check before injecting poet_id
        $album->description = $request->post('description');
        $album->save();

        return redirect()->back()->with('success', 'تم تعديل البيانات بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Album  $album
     * @return \Illuminate\Http\Response
     */
    public function destroy(Album $album)
    {
        // Delete files first
        Storage::delete($album->thumbnail);

        // Delete record
        $album->delete();

        return redirect()->back()->with('success', 'تم المسح بنجاح!');
    }
}