<?php

namespace App\Http\Controllers;

use App\Podcast;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ChartController extends Controller
{
    /**
     *  All Podcast
     */
    public function podcasts()
    {
        $podcasts = Podcast::join('recently_playeds as rp', 'podcasts.id', '=', 'rp.podcast_id')
            ->select('podcasts.id', DB::raw('sum(seconds) as `seconds`'), 'rp.created_at as datetmime', DB::raw("DATE_FORMAT(rp.created_at, '%Y-%m-%d') date"))
            ->where('rp.created_at', '>', now()->subDay(29))
            ->where('rp.created_at', '<=', now())
            ->groupBy('date')
            ->get();

        // return $podcasts;

        return view('charts.podcasts', compact('podcasts'));
    }

    /**
     *  Specific Podcast
     */
    public function podcast(Podcast $podcast)
    {
        $podcasts = Podcast::join('recently_playeds as rp', 'podcasts.id', '=', 'rp.podcast_id')
            ->select('podcasts.id', DB::raw('sum(seconds) as `seconds`'), 'rp.created_at as datetmime', DB::raw("DATE_FORMAT(rp.created_at, '%Y-%m-%d') date"))
            ->where('rp.podcast_id', $podcast->id)
            ->where('rp.created_at', '>', now()->subDay(29))
            ->where('rp.created_at', '<=', now())
            ->groupBy('date')
            ->get();

        // return $podcasts;

        return view('charts.podcast', compact('podcasts'));
    }
}