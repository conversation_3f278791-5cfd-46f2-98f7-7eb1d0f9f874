body {
    font-family: 'Cairo', 'sans-serf' !important;
}

.red_star {
    color: #ff5500 !important;
}

.invalid-feedback {
    display: inline-block !important;
}

.modal {
    top: 50px !important;
}

.modal-backdrop {
    display: none !important;
}

.card {
    // padding: 15px !important;
    color: #212529 !important;
    margin: 8px 0 25px 0 !important;
    border-radius: 10px !important;
    border: unset !important;
    box-shadow: unset !important;
}

.card-header {
    background-color: #FFF !important;
}

.card-header:first-child {
    border-radius: calc(0.9rem - 1px) calc(0.9rem - 1px) 0 0 !important;
}

.card-footer:last-child {
    border-radius: 0 0 calc(0.9em - 1px) calc(0.9em - 1px) !important;
}

.app-theme-white.app-container {
    background: #f1f2f7;
}

.app-header__logo .logo-src {
    background: unset;
    font-weight: bold;
    width: unset;
}

.app-header__logo .logo-src img {
    width: 50px;
    height: 50px;
    position: absolute;
    top: 5px;
}

.app-page-title .page-title-icon {
    margin: 0 0 0 20px !important;
    padding: 0;
    width: 40px;
    height: 40px;
}

.app-sidebar__heading {
    color: #cb2d3e;
}

.vertical-nav-menu li a.mm-active {
    color: #cb2d3e !important;
    font-weight: bold;
}

.vertical-nav-menu li a:hover {
    background: #f7b731;
}

.search-wrapper .input-holder .search-icon span:before {
    background: #cb2d3e;
}

.search-wrapper .input-holder .search-icon span:after {
    border: 2px solid #cb2d3e;
}

.hamburger.is-active .hamburger-inner,
.hamburger.is-active .hamburger-inner::before,
.hamburger.is-active .hamburger-inner::after {
    background-color: #cb2d3e;
}

.hamburger-inner,
.hamburger-inner::before,
.hamburger-inner::after {
    background-color: #cb2d3e;
}

.table thead th {
    border-top: 0;
}

.pagination {
    margin-bottom: 0 !important;
    padding-right: 0 !important;
}

.page-item:last-child .page-link {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

.page-item:first-child .page-link {
    margin-right: 0;
    border-left: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
}

.app-theme-white .app-page-title {
    background: #f1f2f7;
}

.app-page-title {
    margin: -30px -30px 0px;
}

.app-page-title .page-title-heading {
    color: #555555;
}

.vertical-nav-menu i.metismenu-state-icon,
.vertical-nav-menu i.metismenu-icon {
    opacity: 1 !important;
    color: #cb2d3e;
}

.table-image {
    width: 30px;
    height: 30px;
    object-fit: scale-down;
    margin: 0 15px;
    border-radius: 50%;
    border: 2px solid #FFF;
    box-shadow: 0 0px 10px -3px #cb2d3e;
}

.table th,
.table td {
    border-top: 1px solid #f2f2f2 !important;
}

.table tr:first-child th,
.table tr:first-child td {
    border: 0 !important;
}

.vbox-overlay {
    background: rgba(194, 227, 217, 0.61) none repeat scroll 0% 0% !important
}

.venoframe,
.vbox-inline {
    min-height: 90vh !important;
}

.sk-child.sk-double-bounce1 {
    background-color: #cb2d3e !important;
}

.dash_counter {
    font-size: 36px !important;
    color: #777777;
}


/**********  Toggle Switch Start  ************/


/* The switch - the box around the slider */

#has-switch .switch {
    position: relative;
    display: inline-block;
    width: 38px;
    height: 22px;
    float: right;
    margin-bottom: 0;
}


/* Hide default HTML checkbox */

#has-switch .switch input {
    display: none;
}


/* The slider */

#has-switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: 0.4s;
    transition: 0.4s;
    border-radius: 50px;
}

#has-switch .slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    -webkit-transition: 0.4s;
    transition: 0.4s;
    border-radius: 50px;
}

#has-switch input.default:checked+.slider {
    background-color: #444;
}

#has-switch input.primary:checked+.slider {
    background-color: #2196f3;
}

#has-switch input.success:checked+.slider {
    background-color: #8bc34a;
}

#has-switch input.info:checked+.slider {
    background-color: #3de0f5;
}

#has-switch input.warning:checked+.slider {
    background-color: #ffc107;
}

#has-switch input.danger:checked+.slider {
    background-color: #f44336;
}

#has-switch input:focus+.slider {
    box-shadow: 0 0 1px #2196f3;
}

#has-switch input:checked+.slider:before {
    -webkit-transform: translateX(16px);
    -ms-transform: translateX(16px);
    transform: translateX(16px);
}


/* Rounded sliders */

#has-switch .slider.round {
    border-radius: 34px;
}

#has-switch .slider.round:before {
    border-radius: 50%;
}


/**********  Toggle Switch End  ************/
