<!doctype html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Language" content="ar">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>@yield('page_title') - {{ config('app.name', 'Booktown') }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no" />
    {{-- <meta name="description" content=""> --}}
    <meta name="msapplication-tap-highlight" content="no">

    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('css/venobox.css') }}" rel="stylesheet">
    {{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.14.1/lodash.min.js"></script> --}}
    <link href="{{ asset('css/rtl.css') }}" rel="stylesheet">

    @yield('head')

</head>
<body>
    <div class="app-container app-theme-white body-tabs-shadow fixed-sidebar fixed-header" id="app">
        <div class="app-header header-shadow">
            <div class="app-header__logo">
            <a href="{{ route('dashboard') }}" class="logo-src"><img src="{{ asset('images/book-logo.png') }}" alt=""></a>
                <div class="header__pane mr-auto">
                    <div>
                        <button type="button" class="hamburger close-sidebar-btn hamburger--elastic" data-class="closed-sidebar">
                            <span class="hamburger-box">
                                <span class="hamburger-inner"></span>
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="app-header__mobile-menu">
                <div>
                    <button type="button" class="hamburger hamburger--elastic mobile-toggle-nav">
                        <span class="hamburger-box">
                            <span class="hamburger-inner"></span>
                        </span>
                    </button>
                </div>
            </div>
            <div class="app-header__menu">
                <span>
                    <button type="button" class="btn-icon btn-icon-only btn btn-primary btn-sm mobile-toggle-header-nav">
                        <span class="btn-icon-wrapper">
                            <i class="fa fa-ellipsis-v fa-w-6"></i>
                        </span>
                    </button>
                </span>
            </div>
            <div class="app-header__content">
                <div class="app-header-left">
                    {{-- <div class="search-wrapper">
                        <div class="input-holder">
                            <input type="text" class="search-input" placeholder="بحث..">
                            <button class="search-icon"><span></span></button>
                        </div>
                        <button class="close"></button>
                    </div> --}}
                    <ul class="header-menu nav">
                        {{-- <li class="nav-item">
                            <a href="javascript:void(0);" class="nav-link">
                                <i class="nav-link-icon fa fa-database"> </i>
                                Statistics
                            </a>
                        </li> --}}
                    </ul>
                </div>
                <div class="app-header-right">
                    <div class="header-btn-lg pr-0">
                        <div class="widget-content p-0">
                            <div class="widget-content-wrapper">
                                <div class="widget-content-left">
                                    <div class="btn-group">
                                        <a data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="p-0 btn ml-1 mr-1">
                                            {{-- <i class="fa fa-angle-down opacity-8"></i> --}}
                                            <img width="42" class="rounded-circle" src="/images/avatars/2.jpg" alt="">
                                        </a>
                                        {{-- <div tabindex="-1" role="menu" aria-hidden="true" class="dropdown-menu dropdown-menu-right">
                                            <button type="button" tabindex="0" class="dropdown-item">الملف الشخصي</button>
                                            <div tabindex="-1" class="dropdown-divider"></div>
                                            <button type="button" tabindex="0" class="dropdown-item">خروج</button>
                                        </div> --}}
                                        <div class="d-md-none d-sm-inline-block m-auto">
                                            <form action="{{ route('logout') }}" method="POST">
                                                @csrf
                                                <button type="submit" class="btn-shadow p-1 btn btn-primary btn-sm">
                                                    <i class="fa text-white fa-sign-out-alt pr-1 pl-1"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="widget-content-left  ml-3 header-user-info">
                                    <div class="widget-heading">
                                        {{ Auth::user()->name }}
                                    </div>
                                    <div class="widget-subheading">
                                        مشرف
                                    </div>
                                </div>
                                <div class="widget-content-right header-user-info ml-3">
                                <form action="{{ route('logout') }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn-shadow p-1 btn btn-primary btn-sm">
                                        <i class="fa text-white fa-sign-out-alt pr-1 pl-1"></i>
                                    </button>
                                </form>
                                    {{-- <button type="button" class="btn-shadow p-1 btn btn-primary btn-sm show-toastr-example">
                                        <i class="fa text-white fa-sign-out-alt pr-1 pl-1"></i>
                                    </button> --}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="app-main">
            <div class="app-sidebar sidebar-shadow">
                <div class="app-header__logo">
                    <div class="logo-src">{{ env('APP_NAME') }}</div>
                    <div class="header__pane mr-auto">
                        <div>
                            <button type="button" class="hamburger close-sidebar-btn hamburger--elastic" data-class="closed-sidebar">
                                <span class="hamburger-box">
                                    <span class="hamburger-inner"></span>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="app-header__mobile-menu">
                    <div>
                        <button type="button" class="hamburger hamburger--elastic mobile-toggle-nav">
                            <span class="hamburger-box">
                                <span class="hamburger-inner"></span>
                            </span>
                        </button>
                    </div>
                </div>
                <div class="app-header__menu">
                    <span>
                        <button type="button" class="btn-icon btn-icon-only btn btn-primary btn-sm mobile-toggle-header-nav">
                            <span class="btn-icon-wrapper">
                                <i class="fa fa-ellipsis-v fa-w-6"></i>
                            </span>
                        </button>
                    </span>
                </div>
                @include('layouts.base_sidebar')
            </div>
            <div class="app-main__outer">
                <div class="app-main__inner">
                    <div class="app-main__inner_container">
                        <div class="app-page-title">
                            <div class="page-title-wrapper">
                                <div class="page-title-heading">
                                    <div class="page-title-icon">
                                        @yield('page_icon')
                                    </div>
                                    <div>
                                        @yield('page_title')
                                        <div class="page-title-subheading">@yield('page_description')</div>
                                    </div>
                                </div>
                                <div class="page-title-actions">
                                    @yield('page_action')
                                </div>
                            </div>
                        </div>
                    @yield('content')
                    </div>
                </div>
                <div class="app-wrapper-footer">
                    <div class="app-footer">
                        @include('layouts.base_footer')
                    </div>
                </div>
            </div>
        </div>

    @include('layouts.toasts.toasts-message')

</div>


<script type="text/javascript" src="{{ asset('js/app.js') }}"></script>
<script type="text/javascript" src="{{ asset('js/venobox.min.js') }}"></script>

@yield('script')

<script>

// Confirm deletion
function confirmDeletion(item_id) {
    return window.confirm('سيتم الحذف نهائيا، هل انت متأكد؟') ? $('#destroy-form-' + item_id).submit() : false
}

</script>

</body>
</html>
