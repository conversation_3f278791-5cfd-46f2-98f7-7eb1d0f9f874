<!doctype html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Language" content="ar">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>@yield('title')</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no" />
    {{-- <meta name="description" content=""> --}}
    <meta name="msapplication-tap-highlight" content="no">

    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('css/venobox.css') }}" rel="stylesheet">

    <link href="{{ asset('css/rtl.css') }}" rel="stylesheet">

</head>
<body>
    <div class="container mt-5 mb-5">
        <div class="app-main__inner">
                    <div class="app-main__inner_container">
                    @yield('content')
                    </div>
                </div>
    </div>

    @include('layouts.toasts.toasts-message')

<script type="text/javascript" src="{{ asset('js/app.js') }}"></script>
<script type="text/javascript" src="{{ asset('js/venobox.min.js') }}"></script>


@yield('script')

<script>

// Confirm deletion
function confirmDeletion(item_id) {
    return window.confirm('سيتم الحذف نهائيا، هل انت متأكد؟') ? $('#destroy-form-' + item_id).submit() : false
}

</script>

</body>
</html>
