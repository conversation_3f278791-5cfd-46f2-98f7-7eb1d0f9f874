<!doctype html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Language" content="ar">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>@yield('title') - {{ env('APP_NAME') }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no" />
    {{-- <meta name="description" content=""> --}}
    <meta name="msapplication-tap-highlight" content="no">

    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('css/venobox.css') }}" rel="stylesheet">
    {{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.14.1/lodash.min.js"></script> --}}
    <link href="{{ asset('css/rtl.css') }}" rel="stylesheet">

    <style>
        .app-main__outer {
            padding-right: unset !important;
        }
    </style>
    @yield('head')

</head>
<body>
    <div class="app-container app-theme-white body-tabs-shadow fixed-sidebar fixed-header" id="app">
        <div class="app-header header-shadow">
            <div class="app-header__logo">
            <a href="{{ route('dashboard') }}" class="logo-src"><img src="{{ asset('images/book-logo.png') }}" alt=""></a>

            </div>
            <div class="app-header__mobile-menu">
                <div>
                    <button type="button" class="hamburger hamburger--elastic mobile-toggle-nav">
                        <span class="hamburger-box">
                            <span class="hamburger-inner"></span>
                        </span>
                    </button>
                </div>
            </div>
            <div class="app-header__menu">
                <span>
                    <button type="button" class="btn-icon btn-icon-only btn btn-primary btn-sm mobile-toggle-header-nav">
                        <span class="btn-icon-wrapper">
                            <i class="fa fa-ellipsis-v fa-w-6"></i>
                        </span>
                    </button>
                </span>
            </div>
            <div class="app-header__content">

            </div>
        </div>
        <div class="app-main">
            <div class="app-main__outer">
                <div class="app-main__inner">
                    <div class="app-main__inner_container mt-5">

                    @yield('content')
                    </div>
                </div>
                <div class="app-wrapper-footer">
                    <div class="app-footer">
                        @include('layouts.base_footer')
                    </div>
                </div>
            </div>
        </div>

    @include('layouts.toasts.toasts-message')

</div>


<script type="text/javascript" src="{{ asset('js/app.js') }}"></script>
<script type="text/javascript" src="{{ asset('js/venobox.min.js') }}"></script>

@yield('script')

<script>

// Confirm deletion
function confirmDeletion(item_id) {
    return window.confirm('سيتم الحذف نهائيا، هل انت متأكد؟') ? $('#destroy-form-' + item_id).submit() : false
}

</script>

</body>
</html>
