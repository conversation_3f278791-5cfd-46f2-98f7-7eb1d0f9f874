<!doctype html>
<html>

<head>
    <title>Line Chart</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.3/Chart.min.css">
	<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.3/Chart.min.js"></script>
<script>

    'use strict';

    window.chartColors = {
        red: 'rgb(255, 99, 132)',
        orange: 'rgb(255, 159, 64)',
        yellow: 'rgb(255, 205, 86)',
        green: 'rgb(75, 192, 192)',
        blue: 'rgb(54, 162, 235)',
        purple: 'rgb(153, 102, 255)',
        grey: 'rgb(201, 203, 207)'
    };

</script>

    <style>
	canvas{
		-moz-user-select: none;
		-webkit-user-select: none;
		-ms-user-select: none;
	}
	</style>
</head>

<body>
	<div style="width:100%;">
		<canvas id="mycanvas"></canvas>
	</div>

	<script>

        <?php $max = 0; $j = 0; ?>

		var MONTHS = [
            @for($i = 29; $i >= intval(0); $i--)
                '{{ now()->subDay($i)->format("m/d") }}',
            @endfor
        ];

		var data = [
            [
                @for($i = 0 ; $i < 30; $i++)

                    @if(isset($podcasts[$j]) && $podcasts[$j]->date == (now()->subDay(29)->addDay($i)->format("Y-m-d")) )

                        <?php if ($podcasts[$j]->seconds/60 > $max){ $max = $podcasts[$j]->seconds/60; } ?>
                        '{{ number_format($podcasts[$j++]->seconds/60, 2) }}',
                    @else
                        '0',
                    @endif
                @endfor
            ],
        ]

		var config = {
			type: 'line',
			data: {
				labels: MONTHS,
				datasets: [{
					label: 'دقائق الإستماع',
					backgroundColor: window.chartColors.orange,
					borderColor: window.chartColors.red,
					data: data[0],
					fill: true,
				}]
			},
			options: {
				responsive: true,
				title: {
					display: true,
					text: 'مخطط عدد دقائق اﻹستماع اليومي ﻵخر 30 يوم'
				},
				tooltips: {
					mode: 'index',
					intersect: false,
				},
				hover: {
					mode: 'nearest',
					intersect: true
				},
				scales: {
					xAxes: [{
						display: true,
						scaleLabel: {
							display: true,
							labelString: 'اليوم'
						}
					}],
					yAxes: [{
						display: true,
						scaleLabel: {
							display: true,
							labelString: 'الدقائق'
						},
						ticks: {
							min: 0,
							max: {{ $max }},

							// forces step size to be 5 units
							stepSize: 5
						}
					}]
				}
			}
		};

		window.onload = function() {
			var ctx = document.getElementById('mycanvas').getContext('2d');
			window.myLine = new Chart(ctx, config);
		};

	</script>
</body>

</html>
