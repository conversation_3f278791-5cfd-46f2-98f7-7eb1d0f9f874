
@extends('layouts.base')

@section('page_icon')
    <i class="pe-7s-news-paper icon-gradient bg-amy-crisp"></i>
@endsection

@section('page_title')
    الصفحات
@endsection

@section('page_action')
    <a href="{{ route('dynamicPage.create') }}" class="btn btn-success btn-sm" title="إضافة جديد"><i class="fas fa-plus"></i></a>
@endsection

@section('content')
    <div class="main-card mb-3 card">
        <div class="card-body">
            {{-- <h5 class="card-title">Table responsive</h5> --}}
            <div class="table-responsive table-hover">
                <table class="mb-0 table">
                    <thead>
                        @if(count($dynamicPages))
                            <tr>
                                <th scope="row">#</th>
                                <th>العنوان</th>
                                <th>الظهور</th>
                                <th>UUID</th>
                                <th class="text-center">خيارات</th>
                            </tr>
                        @else
                            <div class="text-center">
                                لا توجد صفحات
                            </div>
                        @endempty

                    </thead>
                    <tbody>


                        @foreach ($dynamicPages as $dynamicPage)
                        <tr>
                            <th scope="row">{{ Help::tableIndex($dynamicPages, $loop) }}</th>
                            <td>{{ $dynamicPage->title_ar }}</td>
                            <td>
                                @if($dynamicPage->is_visible)
                                    <span class="badge badge-success">مرئي</span>
                                @else
                                    <span class="badge badge-light">مخفي</span>
                                @endif
                            </td>
                            <td>{{ $dynamicPage->uuid }}</td>
                            <td class="text-center">
                                <a class="venobox btn btn-light btn-sm" data-vbtype="iframe" data-title="Chart" href="{{ route('dynamicPage.show', $dynamicPage) }}"><i class="fas fa-tv"></i> معاينة</a>

                                @if($dynamicPage->is_visible)
                                    <a href="{{ route('dynamicPage.toggle.visibility', $dynamicPage) }}" class="btn btn-light text-danger btn-sm"><i class="fas fa-eye-slash"></i> إخفاء</a>
                                @else
                                    <a href="{{ route('dynamicPage.toggle.visibility', $dynamicPage) }}" class="btn btn-light text-success btn-sm"><i class="fas fa-eye"></i> إظهار</a>
                                @endif
                                <a href="{{ route('dynamicPage.edit', $dynamicPage) }}" class="btn btn-light text-info btn-sm"><i class="fas fa-edit"></i> تعديل</a>
                                <button class="btn btn-light text-danger btn-sm" onclick="confirmDeletion({{ $dynamicPage->id }})"><i class="fas fa-times"></i> حذف</button>
                                <form action="{{ route('dynamicPage.destroy', $dynamicPage) }}" method="POST" id="destroy-form-{{ $dynamicPage->id }}">@csrf @method('DELETE') </form>
                            </td>
                        </tr>

                        @endforeach

                    </tbody>
                </table>
            </div>
            <div class="mt-2">
                {{ $dynamicPages->render() }}
            </div>
        </div>
    </div>
@endsection
