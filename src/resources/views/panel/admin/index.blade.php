
@extends('layouts.base')

@section('page_icon')
    <i class="pe-7s-user icon-gradient bg-amy-crisp"></i>
@endsection

@section('page_title')
    المشرفين
@endsection

@section('page_action')
    <a href="{{ route('admin.create') }}" class="btn btn-success btn-sm" title="إضافة جديد"><i class="fas fa-plus"></i></a>
@endsection

@section('content')
    <div class="main-card mb-3 card">
        <div class="card-body">
            {{-- <h5 class="card-title">Table responsive</h5> --}}
            <div class="table-responsive table-hover">
                <table class="mb-0 table">
                    <thead>
                        @if(count($admins))
                            <tr>
                                <th scope="row">#</th>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th class="text-center">الصلاحيات</th>
                                <th class="text-center">خيارات</th>
                            </tr>
                        @else
                            <div class="text-center">
                                لا توجد تصنيفات
                            </div>
                        @endempty

                    </thead>
                    <tbody>


                        @foreach ($admins as $admin)
                        <tr>
                            <th scope="row">{{ Help::tableIndex($admins, $loop) }}</th>
                            <td>{{ $admin->name }}</td>
                            <td>{{ $admin->email }}</td>
                            <td class="text-center">
                                @foreach($admin->roles as $role)
                                    <span class="badge-large badge badge-{{ Arr::random(['success', 'dark', 'info', 'primary', 'secondary']) }}">{{ $role->name }}</span>
                                @endforeach
                                @if(!count($admin->roles))
                                    <span>لا توجد صلاحيات</span>
                                @endif
                            </td>
                            <td class="text-center">
                                <a href="{{ route('admin.edit', $admin) }}" class="btn btn-light text-info btn-sm"> تعديل</a>
                                <button class="btn btn-light text-danger btn-sm" onclick="confirmDeletion({{ $admin->id }})"> حذف</button>
                                <form action="{{ route('admin.destroy', $admin) }}" method="POST" id="destroy-form-{{ $admin->id }}">@csrf @method('DELETE') </form>
                            </td>
                        </tr>

                        @endforeach

                    </tbody>
                </table>
            </div>
            <div class="mt-2">
                {{ $admins->render() }}
            </div>
        </div>
    </div>
@endsection
