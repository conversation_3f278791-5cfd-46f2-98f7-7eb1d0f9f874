<template>
  <div>
    <div class="main-card mb-3 card">
      <div class="card-body">
        <form action method="GET">
          <div class="row">
            <div class="col-md-6">
              <select v-model="selectedProgramId" id="program" class="form-control">
                <option
                  v-for="program in programs"
                  :key="program.id"
                  :value="program.id"
                >{{ program.name_ar }}</option>
              </select>
            </div>
            <div class="col-md-6">
              <input
                type="text"
                class="form-control"
                v-model="podcastQuery"
                placeholder="اسم الملف الصوتي"
              />
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="main-card mb-3 card">
      <div class="card-body">
        <h5 class="card-title w-100">
          الملفات الصوتية
          <span class="mr-3" v-show="isLoadingFiltering">
            <i class="fa fa-spinner fa-spin" aria-hidden="true"></i>
          </span>
        </h5>
        <div class="table-responsive table-hover">
          <table class="mb-0 table">
            <thead v-show="!filteredPodcasts.length">
              <div class="text-center">لا توجد نتائج</div>
            </thead>
            <tbody>
              <tr>
                <th>
                  <label for="check-all">
                    <input type="checkbox" class="ml-3" id="check-all" @click="onCheckAllClicked()" />
                    تحديد الكل
                  </label>
                </th>
                <td>
                  <button
                    class="btn btn-success text-white float-left"
                    type="button"
                    @click="saveCheckedPodcasts()"
                  >
                    <i
                      v-if="isLoadingForSaving"
                      class="fa fa-spinner fa-spin ml-1"
                      aria-hidden="true"
                    ></i>
                    <i v-else class="fa fa-save ml-1" aria-hidden="true"></i> حفظ
                  </button>
                </td>
              </tr>
              <tr v-for="(podcast, index) in filteredPodcasts" :key="podcast.id">
                <th>
                  <span class="ml-3">{{ index +1 }}</span>
                  <input
                    type="checkbox"
                    class="ml-3"
                    :checked="podcast.checked"
                    v-if="!getPlaylistsIDs(podcast.playlists).includes(playlist.id)"
                    @click="onCheckboxInputClicked(podcast)"
                  />
                  <span v-else class="mr-3 ml-3"></span>
                  {{ podcast.name_ar }}
                </th>
                <td class="text-left">
                  <span v-show="getPlaylistsIDs(podcast.playlists).includes(playlist.id)">
                    <span class="badge badge-light p-2 ml-3 text-success">مضاف مسبقاً</span>
                    <button
                      class="btn btn-sm btn-light text-danger"
                      @click="detachPodcast(podcast)"
                    >
                      <i class="fa fa-times ml-2" aria-hidden="true"></i> حذف من القائمة
                    </button>
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "add-podcasts-to-playlist",
  props: {
    programs: {
      default: []
    },
    selectedProgram: {
      type: Object,
      default: null
    },
    playlist: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      SEARCH_URL: "/dashboard/programs/playlists/podcasts/json",
      SAVE_PODCCASTS_URL: `/dashboard/playlists/${this.playlist.id}/podcasts/attach/json`,
      podcastQuery: "",
      selectedProgramId: this.selectedProgram.id,
      filteredPodcasts: [],
      isLoadingFiltering: false,
      isLoadingForSaving: false,
      checkAll: false,
      detachedPodcastID: 0
    };
  },
  created() {
    this.getPodacst();
  },
  watch: {
    selectedProgramId() {
      this.getPodacst();
    },

    podcastQuery() {
      this.getPodacst();
    }
  },
  computed: {},
  methods: {
    getPodacst() {
      let form = {
        program: this.selectedProgramId,
        podcast: this.podcastQuery
      };
      this.isLoadingFiltering = true;
      axios
        .get(this.SEARCH_URL, {
          params: form
        })
        .then(res => {
          this.isLoadingFiltering = false;
          this.filteredPodcasts = res.data;
          //   console.log(res);
        })
        .catch(err => {
          this.isLoadingFiltering = false;
          console.error(err);
        });
    },
    saveCheckedPodcasts() {
      this.isLoadingForSaving = true;
      let podcasts = this.getCheckedPodcastsIDs();

      axios
        .post(this.SAVE_PODCCASTS_URL, { podcasts: podcasts })
        .then(res => {
          console.log(res);
          this.isLoadingForSaving = false;

          if (res.data.status == "success") {
            this.$toastr.s(res.data.message);
            this.getPodacst();
          } else if (res.data.status == "error") {
            this.$toastr.s(res.data.message);
          }
        })
        .catch(err => {
          console.error(err);
          this.isLoadingForSaving = false;
        });
    },
    detachPodcast(podcast) {
      this.detachedPodcastID = podcast.id;
      let DELELTE_PODCCAST_URL = `/dashboard/playlists/${this.playlist.id}/podcasts/${this.detachedPodcastID}/detach/json`;

      axios
        .delete(DELELTE_PODCCAST_URL)
        .then(res => {
          console.log(res);
          if (res.data.status == "success") {
            this.$toastr.s(res.data.message);
            this.getPodacst();
          } else if (res.data.status == "error") {
            this.$toastr.s(res.data.message);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    onCheckboxInputClicked(podcast) {
      this.filteredPodcasts.map(p => {
        if (p.id == podcast.id) {
          p.checked = !p.checked;
        }
        return p;
      });
    },
    onCheckAllClicked() {
      this.checkAll = !this.checkAll;
      this.filteredPodcasts = this.filteredPodcasts.map(p => {
        p.checked = this.checkAll;
        return p;
      });
    },
    getCheckedPodcastsIDs() {
      let list = [];
      this.filteredPodcasts.filter(p => {
        if (p.checked) {
          list.push(p.id);
        }
      });
      return list;
    },
    getPlaylistsIDs(playlists) {
      let list = [];
      playlists.filter(p => {
        list.push(p.id);
      });
      return list;
    }
  }
};
</script>

<style>
</style>
