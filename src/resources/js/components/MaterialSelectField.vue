<template>
<div :class="{ multiple:multiple }">
    <div class="input-group" dir="ltr">
        <div class="input-group-prepend" v-show="addUrl">
            <span
                class="input-group-tex btn btn-light"
                @click="refresh"
            >
                <i
                    class="fas fa-sync-alt"
                    :class="{ 'fa-spin': isRefreshing }"
                    aria-hidden="true"
                    title="إعادة تحديث القائمة"
                ></i>
            </span>
            <a
                :href="addUrl"
                target="_blank"
                class="input-group-tex btn btn-success"
                title="إضافة جديد"
            >
                <i class="fas fa-plus" aria-hidden="true"></i>
            </a>
        </div>
        <select
            class="custom-select"
            :class="{ 'is-invalid': error }"
            :name="multiple ? name+'[]' : name"
            dir="rtl"
            :multiple="multiple"
        >
            <option
                v-for="model in models"
                :key="model.id"
                :value="model.id"
                :selected="(!multiple && selected == model.id) || (multiple && selected.includes(model.id))"
                >{{ model.name ? model.name : model.name_ar }}</option
            >
        </select>
    </div>
    <small class="text-muted" v-show="multiple">إختيار متعدد (Ctrl + Click)</small>
    <span v-show="error" class="invalid-feedback d-block" role="alert">
        <strong>{{ error }}</strong>
    </span>
</div>
</template>

<script>
export default {
    name: "material-select-field",
    props: {
        name: {
            type: String,
            required: true
        },
        modelSet: {
            type: Array,
            default: []
        },
        selected: {
            type: String,
            default: null
        },
        error: {
            default: null
        },
        addUrl: {
            type: String,
            required: false
        },
        multiple: {
            type: Boolean,
            default: false,
            required: false
        }
    },

    data: function() {
        return {
            models: this.modelSet,
            isRefreshing: false
        };
    },

    methods: {
        refresh() {
            // console.log("plural", $Pluralize("test"));

            let url = this.getSuitableURL();

            this.isRefreshing = true;
            axios
                .get(url)
                .then(res => {
                    setTimeout(() => {
                        this.isRefreshing = false;
                    }, 2000);
                    this.models = res.data;
                })
                .catch(e => {
                    this.isRefreshing = false;
                    console.log("error: ", e.message);
                });
        },

        getSuitableURL() {
            let collection = `${this.name}s`;

            // Exceptions
            if (this.name == "category") {
                collection = "categories";
            }

            return `/dashboard/${collection}/json`;
        }
    }
};
</script>

<style scoped>

.multiple .input-group-prepend {
    position: absolute;
    left: 20px;
    top: 10px;
}

.multiple .input-group-tex {
    max-height: 40px;

}

 .multiple .input-group > .input-group-prepend:first-child > .btn:not(:first-child) {
     border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
 }

  .multiple .input-group select {
     border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
 }

</style>
