<template>
  <div id="slide-main">
    <div class="card-body">
      <div class="table-responsive table-striped">
        <table class="mb-0 table">
          <thead>
            <div class="text-center" v-show="!slides.length">لا توجد بيانات</div>
          </thead>
          <tbody>
            <tr v-for="(slide, index) in slides" :key="slide.id">
              <th scope="row">
                {{ index + 1 }}
                <a
                  class="venobox"
                  :href="getStorageUrl(
                            !slide.cover
                                ? slide.slidable.cover
                                : slide.cover
                        )
                    "
                >
                  <img
                    :src="getStorageUrl(
                            !slide.cover
                                ? slide.slidable.cover
                                : slide.cover
                        )
                    "
                    alt="Img"
                    class="table-image"
                  />
                </a>
                {{ slide.slidable.title_ar }} -
                {{ slide.slidable.title_en }}
              </th>
              <td>
                <div id="has-switch">
                  <label class="switch">
                    <!-- <input type="checkbox" :checked="drug.pivot.is_available" class="success"> -->
                    <input
                      type="checkbox"
                      :checked="slide.is_visible"
                      @click="toggleVisibility(slide)"
                      class="success"
                    />
                    <span class="slider"></span>
                  </label>
                </div>
              </td>
              <td class="text-left">
                <button
                  class="btn btn-success"
                  @click="shiftTop(slide, index)"
                  :disabled="index == 0"
                >
                  <i class="fa fa-arrow-up"></i>
                </button>
                <button
                  class="btn btn-success"
                  @click="shiftBottom(slide, index)"
                  :disabled="index == slides.length - 1"
                >
                  <i class="fa fa-arrow-down"></i>
                </button>
                <!-- <a href="{{ route('slide.edit', $slide) }}" class="btn btn-info btn-sm"><i class="fas fa-edit"></i> تعديل</a>
                                <button class="btn btn-danger btn-sm" onclick="confirmDeletion({{ $slide->id }})"><i class="fas fa-close"></i> حذف</button>
                <form action="{{ route('slide.destroy', $slide) }}" method="POST" id="destroy-form-{{ $slide->id }}">@csrf @method('DELETE') </form>-->
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "slide-main",
  props: {
    slides: {
      type: Array,
      default: []
    }
  },

  data() {
    return {
      BASE_URL: "/dashboard/slides/",
      slidesList: []
    };
  },

  mounted() {
    this.slidesList = this.slides;
  },

  methods: {
    getStorageUrl(url) {
      return "/" + url.replace("public", "storage");
    },

    shiftTop(slide, index) {
      let slide1_id = slide.id;
      let slide2_id = this.slides[index - 1].id;

      this.callForSwap(slide1_id, slide2_id);
      this.swapListAdapter(slide, this.slides[index - 1]);
    },

    shiftBottom(slide, index) {
      let slide1_id = slide.id;
      let slide2_id = this.slides[index + 1].id;

      this.callForSwap(slide1_id, slide2_id);
      this.swapListAdapter(slide, this.slides[index + 1]);
    },

    callForSwap(slide1_id, slide2_id) {
      axios
        .put(this.BASE_URL + `swap/${slide1_id}/${slide2_id}/json`)
        .then(res => {
          //   console.log(res);
          this.$toastr.s("تم التحديث");
        })
        .catch(err => {
          console.error(err);
          this.$toastr.e("حدث خطأ في الإتصال");
        });
    },

    swapListAdapter(slide1, slide2) {
      // let tempSlides = this.slides;

      let temp1 = slide1;
      let temp2 = slide2;

      this.slides = this.slides.map(slide => {
        if (slide.id == temp1.id) {
          return temp2;
        } else if (slide.id == temp2.id) {
          return temp1;
        }
        return slide;
      });
    },

    toggleVisibility(slide) {
      axios
        .put(this.BASE_URL + "toggle-visibility/" + slide.id + "/json")
        .then(res => {
          // console.log(res);
          this.$toastr.s("تم التحديث");
        })
        .catch(err => {
          console.error(err);
          this.$toastr.e("حدث خطأ في الإتصال");
        });
    }
  }
};
</script>

<style scoped></style>
