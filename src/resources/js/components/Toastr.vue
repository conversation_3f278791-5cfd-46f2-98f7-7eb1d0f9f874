<template>
    <div></div>
</template>
<script>
export default {
    name: "toastr",
    props: {
        success: {
            type: String,
            default: null
        },
        info: {
            type: String,
            default: null
        },
        warning: {
            type: String,
            default: null
        },
        error: {
            type: String,
            default: null
        }
    },
    mounted() {
        console.log("mounted");
        // console.log(this.success);
        this.toastMessages();
    },

    methods: {
        toastMessages() {
            if (this.success) {
                this.$toastr.s(this.success);
            }
            if (this.info) {
                this.$toastr.i(this.info);
            }
            if (this.warning) {
                this.$toastr.w(this.warning);
            }
            if (this.error) {
                this.$toastr.e(this.error);
            }
        }
    }
};
</script>
