<template>
  <div>
    <div class="main-card mb-3 card">
      <div class="card-body">
        <div class="table-responsive table-hover">
          <table class="mb-0 table dataTable">
            <thead>
              <tr v-if="podcasts.length">
                <th>#</th>
                <th>دار النشر</th>
                <th v-show="showPrograms">البرنامج</th>
                <th v-show="showPodcasts">الملف الصوتي</th>
                <th v-show="showLikes">اﻹعجاب</th>
                <th v-show="showSeconds">الإستماع (ثانية)</th>
              </tr>
              <div v-else class="text-center">لا توجد بيانات</div>
            </thead>
            <tbody>
              <tr v-for="(podcast,index) in filteredPodcast" :key="podcast.id">
                <td col="1">
                  <span>{{ index +1 }}</span>
                </td>

                <td>{{ podcast.program.vendor.name }}</td>
                <td v-show="showPrograms">{{ podcast.program.name_ar }}</td>
                <td v-show="showPodcasts">{{ podcast.name_ar }}</td>

                <td v-show="showLikes">{{ !podcast.likes ? 0 : podcast.likes }}</td>
                <td v-show="showSeconds">{{ !podcast.seconds ? 0 : podcast.seconds }}</td>
              </tr>

              <tr>
                <td>#</td>
                <td>{{ vendorsSet.size }}</td>
                <td v-show="showPrograms">{{ programsSet.size }}</td>
                <td v-show="showPodcasts">{{ podcastsSet.size }}</td>
                <td v-show="showLikes">{{ totalLikes }}</td>
                <td v-show="showSeconds">{{ totalSeconds }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "podcasts-report",

  props: {
    podcasts: {
      type: Array,
      default: []
    },
    showPrograms: {
      type: Boolean,
      default: true
    },
    showPodcasts: {
      type: Boolean,
      default: true
    },
    showLikes: {
      type: Boolean,
      default: true
    },
    showSeconds: {
      type: Boolean,
      default: true
    }
  },

  data: function() {
    return {};
  },

  created() {
    if (!this.showPrograms) {
      this.showPodcasts = false;
    }
  },

  computed: {
    podcastsSet() {
      var set = new Set();
      this.podcasts.filter(podcast => {
        set.add(podcast.id);
      });
      return set;
    },

    programsSet() {
      var set = new Set();
      this.podcasts.filter(podcast => {
        set.add(podcast.program_id);
      });
      return set;
    },

    vendorsSet() {
      let set = new Set();
      this.podcasts.filter(podcast => {
        set.add(podcast.program.vendor_id);
      });
      return set;
    },

    podcastOfProgramSet() {
      let set = new Set();
      this.podcasts.filter(podcast => {
        set.add(podcast.program.vendor_id);
      });
      return set;
    },

    totalLikes() {
      let total = 0;
      this.filteredPodcast.filter(podcast => {
        total += parseInt(podcast.likes ? podcast.likes : 0);
      });
      return total;
    },

    totalSeconds() {
      let total = 0;
      this.filteredPodcast.filter(podcast => {
        total += parseInt(podcast.seconds ? podcast.seconds : 0);
      });
      return total;
    },

    filteredPodcast() {
      let _podcasts = this.podcasts;

      if (!this.showPodcasts && this.showPrograms) {
        let programsAr = [];
        _podcasts = [];
        _podcasts = this.podcasts.filter(p => {
          if (programsAr[p.program_id] != undefined) {
            return;
          }
          programsAr[p.program_id] = p.program_id;
          p.seconds = this.calcuateSecondsFromPodcast(p.program_id);
          p.likes = this.calcuateLikesFromPodcast(p.program_id);
          return p;
        });
      }

      if (!this.showPrograms) {
        let vendorAr = [];
        _podcasts = [];
        _podcasts = this.podcasts.filter(p => {
          if (vendorAr[p.program.vendor_id] != undefined) {
            return;
          }
          vendorAr[p.program.vendor_id] = p.program.vendor_id;
          p.seconds = this.calcuateSecondsFromVendor(p.program.vendor_id);
          p.likes = this.calcuateLikesFromVendor(p.program.vendor_id);
          return p;
        });
      }

      return _podcasts;
    }
  },

  methods: {
    calcuateLikesFromPodcast(id) {
      let total = 0;
      this.podcasts.filter(podcast => {
        if (podcast.program_id == id) {
          total += parseInt(podcast.likes ? podcast.likes : 0);
        }
      });
      return total;
    },

    calcuateLikesFromVendor(id) {
      let total = 0;
      this.podcasts.filter(podcast => {
        if (podcast.program.vendor_id == id) {
          total += parseInt(parseInt(podcast.likes) ? podcast.likes : 0);
        }
      });
      return total;
    },

    calcuateSecondsFromPodcast(id) {
      let total = 0;
      this.podcasts.filter(podcast => {
        if (podcast.program_id == id) {
          total += parseInt(podcast.seconds ? podcast.seconds : 0);
        }
      });
      return total;
    },

    calcuateSecondsFromVendor(id) {
      let total = 0;
      this.podcasts.filter(podcast => {
        if (podcast.program.vendor_id == id) {
          total += parseInt(parseInt(podcast.seconds) ? podcast.seconds : 0);
        }
      });
      return total;
    }
  }
};
</script>

<style>
</style>
