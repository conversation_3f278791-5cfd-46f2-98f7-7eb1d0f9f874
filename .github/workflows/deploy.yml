name: laravel test 

on:
    push:
        branches:
            - master

jobs:
  tests:
    name: laravel tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
 
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '7.4'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv
          coverage: none
 
      - name: Run composer install
        working-directory: src
        run: composer install -n --prefer-dist
 
      - name: Prepare Laravel Application
        working-directory: src
        run: |
          cp .env.example .env
          php artisan key:generate
      - name: Run Static Analysis
        working-directory: src
        run: ./vendor/bin/phpunit
  deploy:
    runs-on: ubuntu-latest
  
    needs: tests
  
    steps:
      - name: Deploy via executing remote commands
        uses: appleboy/ssh-action@v1.0.0
        with:
          HOST: ${{ secrets.SSH_HOST }}
          USERNAME: ${{ secrets.USERNAME }}
          PORT: ${{ secrets.SSH_PORT }}
          KEY: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
              cd ${{ secrets.BACKEND_DIR_PATH }}
              git pull origin master
              composer install 
              php artisan migrate
